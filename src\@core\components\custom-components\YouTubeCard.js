import React, { useState } from 'react';
import Card from '@mui/material/Card';
import { Box, Grid, useMediaQuery } from '@mui/material';
import { styled } from '@mui/material/styles'

const VideoFrame = styled('iframe')(({ theme }) => ({
  [theme.breakpoints.down('md')]: {
    borderRadius: '8px',
    width: '100%',
    height: 'auto',
  },
  [theme.breakpoints.up('md')]: {
    borderRadius: '14px',
    height: 400,
    width: '100%'
  },
}))

const YouTubeCard = ({ videoUrl }) => {

  const [isPlaying, setIsPlaying] = useState(false);

  const hidden = useMediaQuery(theme => theme.breakpoints.down('md'))

  // Extract the video ID from the URL
  if (videoUrl) {
    const videoId = new URL(videoUrl).searchParams.get('v');
    const embedUrl = `https://www.youtube.com/embed/${videoId}?autoplay=${isPlaying ? '1' : '0'}`;

    const handleClick = () => {
      setIsPlaying(true);
    };

    return (

      <Box onClick={handleClick} component={'div'} sx={{
        borderRadius: hidden ? 2 : 24,
        width: '100%', height: hidden ? 'auto' : 400
      }} >
        <VideoFrame
          title="YouTube Video"
          src={embedUrl}
          allowFullScreen
        />
      </Box>
    );
  }

  return (<></>);
};

export default YouTubeCard;
