import React, { useContext, useEffect, useState } from "react";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import { Typography, IconButton, Box } from "@mui/material";
import { Close as CloseIcon, CropSquareOutlined, MinimizeSharp } from "@mui/icons-material";
import authConfig from "src/configs/auth";
import {
  getFileUploadPDFHeaders,
  getUrl,
} from "src/helpers/utils";
import axios from "axios";

const ViewDocumentByte = ({ invoiceDetails }) => {

  const [fileContent, setFileContent] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      const url = getUrl(authConfig.invoicesEndpoint) + "/generate/" + invoiceDetails?.id;
      const headers = getFileUploadPDFHeaders();

      try {
        const response = await axios({
          method: "post",
          url: url,
          headers: headers,
          responseType: "arraybuffer", // Get raw byte data
        });

        if (response.status === 200) {
          const pdfBlob = new Blob([response.data], { type: "application/pdf" });
          const pdfUrl = URL.createObjectURL(pdfBlob); // Create PDF URL for iframe
          setFileContent(pdfUrl);
        } else {
          console.error("Document object is not available.");
        }
      } catch (error) {
        console.error("Error fetching document:", error);
      }
    };

    if (invoiceDetails?.id) {
      fetchData();
    }
  }, [invoiceDetails]);

  const onClose = () => {
    if (fileContent) {
      URL.revokeObjectURL(fileContent);
    }
    setFileContent(null);
  };

  const toggleSize = () => {
    setIsLarge((prev) => !prev);
  };

  return (
    <Dialog
      open={Boolean(fileContent)}
      onClose={onClose}
      fullScreen
    >
      <DialogTitle
        sx={{
          position: "relative",
          borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
          p: (theme) => `${theme.spacing(1.75, 4)} !important`,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <Typography>Invoice Document</Typography>
        <Box sx={{ position: "absolute", top: "4px", right: "14px" }}>
          <IconButton
            size="small"
            onClick={onClose}
            sx={{
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                ml: 2,
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
          >
             <CloseIcon style={{ fontSize: 20 }} />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent
        sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            p: 0, // Remove padding for proper centering
            width: "100%",
            height: "100vh", // Full height
          }}
      >
        {fileContent ? (
          <iframe
            src={fileContent}
            title="PDF Viewer"
            style={{ width: "100%", height: "100%", border: "none" }}
          />
        ) : (
          <Typography>Loading...</Typography>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default ViewDocumentByte;
