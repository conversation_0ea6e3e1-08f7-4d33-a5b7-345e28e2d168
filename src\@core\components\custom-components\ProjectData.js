import styled from "@emotion/styled";
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>alog,
  <PERSON>alogA<PERSON>,
  DialogContent,
  DialogTitle,
  FormControl,
  FormHelperText,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
} from "@mui/material";

import { useContext, useState } from "react";

import IconButton from "@mui/material/IconButton";
import { Controller, useForm } from "react-hook-form";

import Icon from "src/@core/components/icon";
import { AuthContext } from "src/context/AuthContext";
import { useAuth } from "src/hooks/useAuth";
import SelectProject from "./SelectProject";
import { toast } from "react-hot-toast";

// ** Styled Component
const MUITableCell = styled(TableCell)(({ theme }) => ({
  padding: "0.55rem !important",
  paddingLeft: "0.75rem !important",
  paddingRight: "0 !important",
  "&:not(:last-child)": {
    paddingRight: `${theme.spacing(2)} !important`,
  },
}));

const projectTypeOptions = [
  {
    value: "REDEVELOPMENT",
    key: "REDEVELOPMENT",
  },
  {
    value: "FUNDING",
    key: "FUNDING",
  },
  {
    value: "MARKETING",
    key: "MARKETING",
  },
];

const ProjectData = ({ data }) => {
  const auth = useAuth();



  const { updateProjectData } = useContext(AuthContext);

  // const [data, setData] = useState([]);

  const [name, setName] = useState("");
  const [contactNo, setContactNo] = useState("");
  const [projectType, setProjectType] = useState("");
  const [id, setId] = useState(null);
  const [editIndex, setEditIndex] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);

  async function submit(data) {

     if (id != null) {
      data.id = id;
    }

    if (!name || !name.trim()) {
      setError("name", { type: "validation", message: "Name is required" });
      return;
    }
    if (!contactNo) {
      setError("contactNo", { type: "validation", message: "Contact number is required" });
      return;
    }
    
    if (!projectType) {
      setError("projectType", { type: "validation", message: "Project type is required" });
      return;
    }
    
    console.log("Submitted data:", data);
    const response = await updateProjectData(data, () => {});
    handleCloseDialog();

  }

  const {
    register,
    setError,
    control,
    reset,
    handleSubmit,
    formState: { errors },clearErrors
  } = useForm();

  const handleDelete = (index) => {
    if (window.confirm("Are you sure you want to delete this item?")) {
      //const newData = [...data];
      //newData.splice(index, 1);
      //setData(newData);
    }
  };

  const handleEdit = (index) => {
    setOpenDialog(true);
    const item = data?.projects[index];
    setId(item.id);
    setEditIndex(index);
    setName(item.name);
    setContactNo(item.contactNo);
    setProjectType(item.projectType);
    
  };

  const handleOpenDialog = () => {
    
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {


    setOpenDialog(false);
    setEditIndex(null);
    setId(null);
    setName("");
    setContactNo("");
    setProjectType("");
    clearErrors(name);
    clearErrors(projectType);
    clearErrors(contactNo);

};

  return (
    <>
      <Grid container spacing={3}>
        <Grid item xs={12} sx={{ textAlign: { xs: "left", lg: "right" } }}>
          <Button variant="contained" onClick={handleOpenDialog} sx={{ mb: 5 }}>
            Add Project Data
          </Button>
        </Grid>
      </Grid>

      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth={"xs"}>
        <DialogTitle>
          {editIndex === null ? "Add Project Data" : "Edit Project Data"}
        </DialogTitle>

        <DialogContent
          sx={{
            position: "relative",
            pt: (theme) => `${theme.spacing(8)} !important`,
            pb: (theme) => `${theme.spacing(5)} !important`,
            px: (theme) => [`${theme.spacing(8)} !important`],
          }}
        >
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <SelectProject
                register={register}
                id="projectType"
                label="Project Type"
                name="projectType"
                nameArray={projectTypeOptions}
                defaultValue={projectType}
                value={projectType}
                onChange={(e) => 
                  {
                  setProjectType(e.target.value);
                  clearErrors(projectType)
                  }}
                error={Boolean(errors.projectType)}
              />
              {errors.projectType && (
                <FormHelperText
                  sx={{ color: "error.main" }}
                  id="validation-projectType"
                >
                  Please select a project type
                </FormHelperText>
              )}
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <Controller
                  name="name"
                  control={control}
                  defaultValue={name}
                  rules={{ required: true }}
                  render={({ field: { value, onChange } }) => (
                    <TextField
                      id="name"
                      label="Name"
                      placeholder="Enter your name"
                      value={name}
                      InputLabelProps={{ shrink: true }}
                      inputProps={{ maxLength: 30 }}
                      error={Boolean(errors.name)}
                      onChange={(e) => {
                        onChange(e.target.value);
                        setName(e.target.value);
                        clearErrors(name); 
                      }}
                      aria-describedby="validation-basic-name"
                    />
                  )}
                />
                {errors.name && (
                  <FormHelperText
                    sx={{ color: "error.main" }}
                    id="validation-basic-name"
                  >
                    Name is required
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <Controller
                  name="contactNo"
                  control={control}
                  rules={{ required: true, pattern: /^[5-9][0-9]*$/ }}
                  defaultValue={contactNo}
                  render={({ field: { value, onChange } }) => (
                    <TextField
                      type="tel"
                      label="Contact Number"
                      placeholder="Enter contact number"
                      InputLabelProps={{ shrink: true }}
                      value={contactNo}
                      onChange={(e) => {
                        onChange(e.target.value);
                        setContactNo(e.target.value);
                        clearErrors(contactNo); 
                      }}
                      error={Boolean(errors.contactNo)}
                      inputProps={{ maxLength: 10 }}
                      aria-describedby="validation-Contact Number"
                    />
                  )}
                />
                {errors.contactNo && (
                  <FormHelperText
                    sx={{ color: "error.main" }}
                    id="validation-Contact Number"
                  >
                    Contact number is required
                  </FormHelperText>
                )}
                {errors.contactNo?.type === "pattern" && (
                  <FormHelperText
                    sx={{ color: "error.main" }}
                    id="validation-Contact Number"
                  >
                    Please enter a valid 10 digit contact number
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button
            variant="outlined"
            color="primary"
            onClick={handleCloseDialog}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            type="submit"
            onClick={handleSubmit(submit)}
          >
            {editIndex === null ? "Submit" : "Update"}
          </Button>
        </DialogActions>
      </Dialog>

      <Grid container spacing={3}>
        <Grid item xs={12}>
          <TableContainer component={Card}>
            <Table>
              <TableHead sx={{ whiteSpace: "nowrap" }}>
                <TableRow>
                  <MUITableCell
                    style={{ fontWeight: "bold", noWrap: "nowrap" }}
                  >
                    Project Type
                  </MUITableCell>
                  <MUITableCell style={{ fontWeight: "bold" }}>
                    Name
                  </MUITableCell>
                  <MUITableCell style={{ fontWeight: "bold" }}>
                    Contact Number
                  </MUITableCell>

                  <MUITableCell style={{ fontWeight: "bold" }}>
                    Actions
                  </MUITableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {data?.projects?.map((item, index) => (
                  <TableRow key={index}>
                    <MUITableCell style={{}}>{item.projectType}</MUITableCell>
                    <MUITableCell style={{}}>{item.name}</MUITableCell>
                    <MUITableCell style={{}}>{item.contactNo}</MUITableCell>

                    <MUITableCell style={{}}>
                      <IconButton
                        sx={{ p: 0, mr: 1, width: 26, height: 26 }}
                        size="small"
                        color="primary"
                        onClick={() => handleEdit(index)}
                      >
                        <Icon icon="tabler:edit" />
                      </IconButton>

                      <IconButton
                        sx={{ p: 0, width: 26, height: 26 }}
                        size="small"
                        color="error"
                        onClick={() => handleDelete(index)}
                      >
                        <Icon icon="tabler:trash" />
                      </IconButton>
                    </MUITableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Grid>
      </Grid>
    </>
  );
};

export default ProjectData;
