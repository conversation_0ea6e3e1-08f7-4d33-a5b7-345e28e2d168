// ** MUI Imports
import Typography from "@mui/material/Typography";
import { useState } from "react";

// ** Custom Components Imports
import AccordionBasic from "src/@core//components/custom-components/AccordionBasic";

// ** Demo Components Imports

import { useTheme } from "@emotion/react";

// ** Styled Component
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContentText,
  DialogTitle,
  Grid,
  Table,
  TableBody,
  TableContainer,
  TableRow,
} from "@mui/material";

//import MUITableCell from "../../MUITableCell";

import PretenderingTab from "src/@core/components/custom-components/PretenderingTab";
import TenderingTab from "src/@core/components/custom-components/TenderingTab";
import ConstructionTab from "src/@core/components/custom-components/ConstructionTab";
import MUITableCell from "src/pages/SP/MUITableCell";
import NavTabConditional from "./NavTabConditional";
import { useRBAC } from "src/pages/permission/RBACContext";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const ServicesView = ({ data, expanded,readPermission,permission }) => {

  
  const { can } = useRBAC();
  // ** Hook
  const theme = useTheme();
  const [state3, setState3] = useState("view");

  const viewClick3 = () => {
    setState3("edit");
  };

  const editClick3 = () => {
    setState3("view");
  };

  const [state4, setState4] = useState("view");

  const viewClick4 = () => {
    setState4("edit");
  };

  const editClick4 = () => {
    setState4("view");
  };
  const [state5, setState5] = useState("view");

  const viewClick5 = () => {
    setState5("edit");
  };

  const editClick5 = () => {
    setState5("view");
  };
  const [preAgreement, setServices] = useState({
    fsiCalculations: {
      isSupported: false,
      price: "",
    },
    dataAndDocumentation: {
      isSupported: false,
      price: "",
    },
    siteAnalysisReport: {
      isSupported: false,
      price: "",
    },
    marketAnalysis: {
      isSupported: false,
      price: "",
    },
    feasibilityReport: {
      isSupported: false,
      price: "",
    },
    financialCOPMOF: {
      isSupported: false,
      price: "",
    },
    cashFlows: {
      isSupported: false,
      price: "",
    },
    technicalFeasibility: {
      isSupported: false,
      price: "",
    },
    deemedConveyance: {
      isSupported: false,
      price: "",
    },
    areaCorrection: {
      isSupported: false,
      price: "",
    },
    plotSubdivision: {
      isSupported: false,
      price: "",
    },
    ctsOffice: {
      isSupported: false,
      price: "",
    },
    bmcFileApprovals: {
      isSupported: false,
      price: "",
    },
    dpRemark2034: {
      isSupported: false,
      price: "",
    },
    searchReport30years: {
      isSupported: false,
      price: "",
    },
    conveyanceDeed: {
      isSupported: false,
      price: "",
    },
    plotPhysicalSurvey: {
      isSupported: false,
      price: "",
    },
    superImposePlotSurvey: {
      isSupported: false,
      price: "",
    },
    individualFlatMeasurement: {
      isSupported: false,
      price: "",
    },
    a79Process: {
      isSupported: false,
      price: "",
    },
  });
  const [tenderingStage, setTenderingServices] = useState({
    tenderingDocuments: {
      isSupported: false,
      price: "",
    },
    preQualificationOfDevelopers: {
      isSupported: false,
      price: "",
    },
    scrutinizationOfTenderDocuments: {
      isSupported: false,
      price: "",
    },
    appointmentOfDevelopers: {
      isSupported: false,
      price: "",
    },
  });
  const [constructionStage, setConstructionServices] = useState({
    societyCoordinationThroughoutProject: {
      isSupported: false,
      price: "",
    },
    materialManagement: {
      isSupported: false,
      price: "",
    },
    periodicalTesting: {
      isSupported: false,
      price: "",
    },
    siteSupervisionAndQualityControl: {
      isSupported: false,
      price: "",
    },
    timeLineMonitoring: {
      isSupported: false,
      price: "",
    },
  });

  const [currentTab, setCurrentTab] = useState("1");
  const [unsavedChanges, setUnsavedChanges] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [pendingTab, setPendingTab] = useState(null);

  const message = `
  <div> 
    <h3> You have unsaved changes. Are you sure you want to switch tabs ?</h3>
  </div>
`;

  const handleTabChange = (event, newValue) => {
    console.log("unsaved state", unsavedChanges);
    if (unsavedChanges) {
      setPendingTab(newValue);
      setDialogOpen(true);
    } else {
      setCurrentTab(newValue);
      setDialogOpen(false);
    }
  };

  const handleDialogClose = (confirm) => {
    if (confirm) {
      setCurrentTab(pendingTab);
      setUnsavedChanges(false);
    }
    setDialogOpen(false);
  };
  return (
    <>
    {/* {can(readPermission) &&   */}
      <Grid>
      <AccordionBasic
        id={"panel-header-1"}
        ariaControls={"panel-content-1"}
        heading={"PMC SERVICES offered"}
        body={
          <>
            <NavTabConditional
              tabContent1={
                <>
                  {state3 === "view" && (
                    <TableContainer
                      sx={{ padding: "4px 6px" }}
                      className="tableBody"
                      onClick={ viewClick3 }
                    >
                      <Table>
                        <TableBody
                          sx={{
                            "& .MuiTableCell-root": {
                              p: `${theme.spacing(1.35, 1.125)} !important`,
                            },
                          }}
                        >
                          <TableRow>
                            <MUITableCell>
                              <Typography sx={{ fontWeight: 600 }}>
                                Service Name:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell>
                              <Typography sx={{ fontWeight: 600 }}>
                                (Yes/No):
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                          <TableRow>
                            <MUITableCell>
                              <Typography style={field}>
                                FSI Calculations:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell>
                              <Typography className="data-field">
                                {data?.preAgreement?.fsiCalculations
                                  ?.isSupported
                                  ? "Yes"
                                  : "No"}
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                          <TableRow>
                            <MUITableCell>
                              <Typography style={field}>
                                Data and Documentation:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell>
                              <Typography className="data-field">
                                {data?.preAgreement?.dataAndDocumentation
                                  ?.isSupported
                                  ? "Yes"
                                  : "No"}
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                          <TableRow>
                            <MUITableCell>
                              <Typography style={field}>
                                Site Analysis Report:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell>
                              <Typography className="data-field">
                                {data?.preAgreement?.siteAnalysisReport
                                  ?.isSupported
                                  ? "Yes"
                                  : "No"}
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                          <TableRow>
                            <MUITableCell>
                              <Typography style={field}>
                                Market Analysis:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell>
                              <Typography className="data-field">
                                {data?.preAgreement?.marketAnalysis
                                  ?.isSupported
                                  ? "Yes"
                                  : "No"}
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                          <TableRow>
                            <MUITableCell>
                              <Typography style={field}>
                                Feasibility Report:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell>
                              <Typography className="data-field">
                                {data?.preAgreement?.feasibilityReport
                                  ?.isSupported
                                  ? "Yes"
                                  : "No"}
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                          <TableRow>
                            <MUITableCell>
                              <Typography style={field}>
                                Financial COPMOF :
                              </Typography>
                            </MUITableCell>
                            <MUITableCell>
                              <Typography className="data-field">
                                {data?.preAgreement?.financialCOPMOF
                                  ?.isSupported
                                  ? "Yes"
                                  : "No"}
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                          <TableRow>
                            <MUITableCell>
                              <Typography style={field}>
                                Cash flows:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell>
                              <Typography className="data-field">
                                {data?.preAgreement?.cashFlows?.isSupported
                                  ? "Yes"
                                  : "No"}
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                          <TableRow>
                            <MUITableCell>
                              <Typography style={field}>
                                Technical Feasibility:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell>
                              <Typography className="data-field">
                                {data?.preAgreement?.technicalFeasibility
                                  ?.isSupported
                                  ? "Yes"
                                  : "No"}
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                          <TableRow>
                            <MUITableCell>
                              <Typography style={field}>
                                Deemed Conveyance
                              </Typography>
                            </MUITableCell>
                            <MUITableCell>
                              <Typography className="data-field">
                                {data?.preAgreement?.deemedConveyance
                                  ?.isSupported
                                  ? "Yes"
                                  : "No"}
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                          <TableRow>
                            <MUITableCell>
                              <Typography style={field}>
                                Area Correction
                              </Typography>
                            </MUITableCell>
                            <MUITableCell>
                              <Typography className="data-field">
                                {data?.preAgreement?.areaCorrection
                                  ?.isSupported
                                  ? "Yes"
                                  : "No"}
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                          <TableRow>
                            <MUITableCell>
                              <Typography style={field}>
                                Plot Subdivision
                              </Typography>
                            </MUITableCell>
                            <MUITableCell>
                              <Typography className="data-field">
                                {data?.preAgreement?.plotSubdivision
                                  ?.isSupported
                                  ? "Yes"
                                  : "No"}
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                          <TableRow>
                            <MUITableCell>
                              <Typography style={field}>
                                CTS office followup
                              </Typography>
                            </MUITableCell>
                            <MUITableCell>
                              <Typography className="data-field">
                                {data?.preAgreement?.ctsOffice?.isSupported
                                  ? "Yes"
                                  : "No"}
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                          <TableRow>
                            <MUITableCell>
                              <Typography style={field}>
                                BMC file for Approvals
                              </Typography>
                            </MUITableCell>
                            <MUITableCell>
                              <Typography className="data-field">
                                {data?.preAgreement?.bmcFileApprovals
                                  ?.isSupported
                                  ? "Yes"
                                  : "No"}
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                          <TableRow>
                            <MUITableCell>
                              <Typography style={field}>
                                DP Remark 2034
                              </Typography>
                            </MUITableCell>
                            <MUITableCell>
                              <Typography className="data-field">
                                {data?.preAgreement?.dpRemark2034?.isSupported
                                  ? "Yes"
                                  : "No"}
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                          <TableRow>
                            <MUITableCell>
                              <Typography style={field}>
                                Search Report 30 years
                              </Typography>
                            </MUITableCell>
                            <MUITableCell>
                              <Typography className="data-field">
                                {data?.preAgreement?.searchReport30years
                                  ?.isSupported
                                  ? "Yes"
                                  : "No"}
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                          <TableRow>
                            <MUITableCell>
                              <Typography style={field}>
                                Conveyance Deed
                              </Typography>
                            </MUITableCell>
                            <MUITableCell>
                              <Typography className="data-field">
                                {data?.preAgreement?.conveyanceDeed
                                  ?.isSupported
                                  ? "Yes"
                                  : "No"}
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                          <TableRow>
                            <MUITableCell>
                              <Typography style={field}>
                                Plot physical Survey
                              </Typography>
                            </MUITableCell>
                            <MUITableCell>
                              <Typography className="data-field">
                                {data?.preAgreement?.plotPhysicalSurvey
                                  ?.isSupported
                                  ? "Yes"
                                  : "No"}
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                          <TableRow>
                            <MUITableCell>
                              <Typography style={field}>
                                Superimpose plot survey with CTS plan
                              </Typography>
                            </MUITableCell>
                            <MUITableCell>
                              <Typography className="data-field">
                                {data?.preAgreement?.superImposePlotSurvey
                                  ?.isSupported
                                  ? "Yes"
                                  : "No"}
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                          <TableRow>
                            <MUITableCell>
                              <Typography style={field}>
                                Individual Flat measurement
                              </Typography>
                            </MUITableCell>
                            <MUITableCell>
                              <Typography className="data-field">
                                {data?.preAgreement?.individualFlatMeasurement
                                  ?.isSupported
                                  ? "Yes"
                                  : "No"}
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                          <TableRow>
                            <MUITableCell>
                              <Typography style={field}>
                                79(a) process
                              </Typography>
                            </MUITableCell>
                            <MUITableCell>
                              <Typography className="data-field">
                                {data?.preAgreement?.a79Process?.isSupported
                                  ? "Yes"
                                  : "No"}
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                        </TableBody>
                      </Table>
                    </TableContainer>
                  )}
                  {state3 === "edit" &&
                    currentTab === "1" &&
                    (data?.preAgreement != null ? (
                      <PretenderingTab
                        setter={setServices}
                        defaultData={data.preAgreement}
                        onCancel={editClick3}
                        setUnsavedChanges={setUnsavedChanges}
                      />
                    ) : (
                      <PretenderingTab
                        setter={setServices}
                        defaultData={preAgreement}
                        onCancel={editClick3}
                        setUnsavedChanges={setUnsavedChanges}
                      />
                    ))}
                </>
              }
              tabContent2={
                <>
                  {state4 === "view" && (
                    <TableContainer
                      sx={{ padding: "4px 6px" }}
                      className="tableBody"
                      onClick={can(permission) ? viewClick4 : null}
                    >
                      <Table>
                        <TableBody
                          sx={{
                            "& .MuiTableCell-root": {
                              p: `${theme.spacing(1.35, 1.125)} !important`,
                            },
                          }}
                        >
                          <TableRow>
                            <MUITableCell>
                              <Typography sx={{ fontWeight: 600 }}>
                                Service Name:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell>
                              <Typography sx={{ fontWeight: 600 }}>
                                (Yes/No):
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                          <TableRow>
                            <MUITableCell>
                              <Typography style={field}>
                                Tendering Documents:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell>
                              <Typography className="data-field">
                                {data?.tenderingStage?.tenderingDocuments
                                  ?.isSupported
                                  ? "Yes"
                                  : "No"}
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                          <TableRow>
                            <MUITableCell>
                              <Typography style={field}>
                                Pre-Qualification of Developers:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell>
                              <Typography className="data-field">
                                {data?.tenderingStage
                                  ?.preQualificationOfDevelopers?.isSupported
                                  ? "Yes"
                                  : "No"}
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                          <TableRow>
                            <MUITableCell>
                              <Typography style={field}>
                                Scrutinization of Tender Documents:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell>
                              <Typography className="data-field">
                                {data?.tenderingStage
                                  ?.scrutinizationOfTenderDocuments
                                  ?.isSupported
                                  ? "Yes"
                                  : "No"}
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                          <TableRow>
                            <MUITableCell>
                              <Typography style={field}>
                                Appointment of Developers:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell>
                              <Typography className="data-field">
                                {data?.tenderingStage?.appointmentOfDevelopers
                                  ?.isSupported
                                  ? "Yes"
                                  : "No"}
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                        </TableBody>
                      </Table>
                    </TableContainer>
                  )}
                  {state4 === "edit" &&
                    currentTab === "2" &&
                    (data?.tenderingStage != null ? (
                      <TenderingTab
                        setter={setTenderingServices}
                        defaultData={data.tenderingStage}
                        onCancel={editClick4}
                        setUnsavedChanges={setUnsavedChanges}
                      />
                    ) : (
                      <TenderingTab
                        setter={setTenderingServices}
                        defaultData={tenderingStage}
                        onCancel={editClick4}
                        setUnsavedChanges={setUnsavedChanges}
                      />
                    ))}
                </>
              }
              tabContent3={
                <>
                  {state5 === "view" && (
                    <TableContainer
                      sx={{ padding: "4px 6px" }}
                      className="tableBody"
                      onClick={can(permission) ? viewClick5 : null}
                    >
                      <Table>
                        <TableBody
                          sx={{
                            "& .MuiTableCell-root": {
                              p: `${theme.spacing(1.35, 1.125)} !important`,
                            },
                          }}
                        >
                          <TableRow>
                            <MUITableCell>
                              <Typography sx={{ fontWeight: 600 }}>
                                Service Name:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell>
                              <Typography sx={{ fontWeight: 600 }}>
                                (Yes/No):
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                          <TableRow>
                            <MUITableCell>
                              <Typography style={field}>
                                Society coordination throughout project:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell>
                              <Typography className="data-field">
                                {data?.constructionStage
                                  ?.societyCoordinationThroughoutProject
                                  ?.isSupported
                                  ? "Yes"
                                  : "No"}
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                          <TableRow>
                            <MUITableCell>
                              <Typography style={field}>
                                Material Management:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell>
                              <Typography className="data-field">
                                {data?.constructionStage?.materialManagement
                                  ?.isSupported
                                  ? "Yes"
                                  : "No"}
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                          <TableRow>
                            <MUITableCell>
                              <Typography style={field}>
                                Periodical Testing:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell>
                              <Typography className="data-field">
                                {data?.constructionStage?.periodicalTesting
                                  ?.isSupported
                                  ? "Yes"
                                  : "No"}
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                          <TableRow>
                            <MUITableCell>
                              <Typography style={field}>
                                Site Supervision and Quality Control:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell>
                              <Typography className="data-field">
                                {data?.constructionStage
                                  ?.siteSupervisionAndQualityControl
                                  ?.isSupported
                                  ? "Yes"
                                  : "No"}
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                          <TableRow>
                            <MUITableCell>
                              <Typography style={field}>
                                Time line monitoring:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell>
                              <Typography className="data-field">
                                {data?.constructionStage?.timeLineMonitoring
                                  ?.isSupported
                                  ? "Yes"
                                  : "No"}
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                        </TableBody>
                      </Table>
                    </TableContainer>
                  )}
                  {state5 === "edit" &&
                    currentTab === "3" &&
                    (data?.constructionStage != null ? (
                      <ConstructionTab
                        setter={setConstructionServices}
                        defaultData={data.constructionStage}
                        onCancel={editClick5}
                        setUnsavedChanges={setUnsavedChanges}
                      />
                    ) : (
                      <ConstructionTab
                        setter={setConstructionServices}
                        defaultData={constructionStage}
                        onCancel={editClick5}
                        setUnsavedChanges={setUnsavedChanges}
                      />
                    ))}
                </>
              }
              currentTab={currentTab}
              handleTabChange={handleTabChange}
            />
          </>
        }
        expanded={expanded}
      />

      <Dialog
        open={dialogOpen}
        onClose={() => handleDialogClose(false)}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogTitle  color="primary.dark" fontWeight={"bold"}>{"Unsaved Changes"}</DialogTitle>
          <DialogContentText
            id="alert-dialog-description"
            color="primary.main"
          >
            <div dangerouslySetInnerHTML={{ __html: message }} />
          
          </DialogContentText>
          <DialogActions>
          <Button
              onClick={() => handleDialogClose(true)}
              color="primary"
              autoFocus
            >
              Yes
            </Button>
            <Button onClick={() => handleDialogClose(false)} color="primary">
              No
            </Button>
           
          </DialogActions>
        </Box>
      </Dialog>
    </Grid>  
    {/* }    */}

    </>
  );
};

export default ServicesView;
