# User Guidelines for Donation Receipt Frontend Application

## 1. UI Component Standards

### DataGrid Tables
Always create MUI `<DataGrid>` tables with these standard properties:

```jsx
<DataGrid
  checkboxSelection
  pagination
  pageSize={pageSize}
  page={page - 1}
  rowsPerPageOptions={rowsPerPageOptions}
  rowCount={rowCount}
  paginationMode="server"
  onPageChange={handlePageChange}
  onPageSizeChange={handlePageSizeChange}
  rowHeight={38}
  headerHeight={38}
/>
```

Required state variables:

```jsx
const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
const [page, setPage] = useState(1);
const [rowCount, setRowCount] = useState(0);
```

### Form Components
- Use `SelectAutoComplete` component for all dropdown selections instead of regular Select component
- When using SelectAutoComplete for template selection, save the 'key' property instead of 'value' property in state
- Always include proper validation and error handling

## 2. Communication Features

### WhatsApp/Email Integration
Use this standard pattern for communication features:

```jsx
// State management
const [communicationMode, setCommunicationMode] = useState(""); // "WHATSAPP" or "EMAIL"
const [sendMessageDialog, setSendMessageDialog] = useState(false);
const [selectedTemplateName, setSelectedTemplateName] = useState("");

// Handler functions
const handleSendWhatsAppClick = () => {
  setCommunicationMode("WHATSAPP");
  setSendMessageDialog(true);
};

const handleSendEmailClick = () => {
  setCommunicationMode("EMAIL");
  setSendMessageDialog(true);
};

// API call pattern
const handleSendMessage = async () => {
  const url = getUrl(authConfig.endpoint) + "/send-communication/" + targetId;
  const data = {
    communicationModeEnums: communicationMode,
    templateIdOrName: selectedTemplateName,
  };
  // ... rest of API call
};
```

## 3. File Upload Handlers

### Standard File Preview Pattern

```jsx
const openDialog = async (index) => {
  setSelectedFileIndex(index);
  const file = selectedFiles[index];

  if (file.type === "application/vnd.openxmlformats-officedocument.wordprocessingml.document") {
    try {
      const arrayBuffer = await file.arrayBuffer();
      const result = await mammoth.convertToHtml({ arrayBuffer });
      setDocxContent(result.value);
    } catch (error) {
      console.error("Error converting DOCX to HTML:", error);
    }
  }

  if (file.type === "application/pdf") {
    try {
      const url = URL.createObjectURL(file);
      setPdfUrl(url);
    } catch (error) {
      console.error("Error creating object URL for PDF:", error);
    }
  }
};

const closeDialog = () => {
  setSelectedFileIndex(null);
  setDocxContent("");
  if (pdfUrl) {
    URL.revokeObjectURL(pdfUrl);
    setPdfUrl(null);
  }
};
```

## 4. API Integration Standards

### Data Fetching with Filters

```jsx
const fetchData = async (currentPage, currentPageSize, selectedFilters) => {
  setLoading(true);
  
  const data = {
    page: currentPage,
    pageSize: currentPageSize,
  };

  // Dynamic filter addition
  selectedFilters?.forEach((filter) => {
    data[filter.key] = filter.value;
  });

  try {
    const response = await axios({
      method: "post",
      url: getUrl(authConfig.endpoint),
      headers: getAuthorizationHeaders(),
      data: data,
    });
    // Handle response
  } catch (error) {
    console.error("Error fetching data:", error);
  } finally {
    setLoading(false);
  }
};
```

### Export Functionality

```jsx
const handleExport = () => {
  const today = new Date();
  const formattedDate = `${today.getDate().toString().padStart(2, "0")}-${(today.getMonth() + 1).toString().padStart(2, "0")}-${today.getFullYear()}`;

  axios({
    method: "post",
    url: getUrl(authConfig.exportEndpoint),
    headers: getAuthorizationHeaders(),
    data: selectedRows,
    responseType: "arraybuffer",
  })
    .then((res) => {
      const workbook = XLSX.read(res.data, { type: "array" });
      const fileName = `export-${formattedDate}.xlsx`;
      XLSX.writeFile(workbook, fileName);
    })
    .catch((error) => {
      console.error("Error exporting:", error);
    });
};
```

## 5. State Management Patterns

### Filter Management

```jsx
const handleApplyFilters = (filters) => {
  setSelectedFilters(filters);
  setSearchingState(true);
};

const handleRemoveFilter = (filterKey) => {
  setSelectedFilters((prevFilters) =>
    prevFilters.filter((filter) => filter.key !== filterKey)
  );
};

const clearAllFilters = () => {
  setSelectedFilters([]);
  setSearchingState(false);
};
```

### Pagination Handlers

```jsx
const handlePageChange = (newPage) => {
  setPage(newPage + 1); // DataGrid uses 0-based indexing
};

const handlePageSizeChange = (newPageSize) => {
  setPageSize(newPageSize);
  setPage(1); // Reset to first page
};
```

## 6. Error Handling & User Feedback

### Message Status Pattern

```jsx
const handleSuccess = () => {
  const message = `
    <div>
      <h3>Operation completed successfully.</h3>
    </div>
  `;
  setDialogMessage(message);
  setMessageStatus("info");
};

const handleFailure = (error) => {
  const message = `
    <div>
      <h3>Operation failed. Please try again later.</h3>
    </div>
  `;
  setDialogMessage(message);
  setMessageStatus("error");
};
```

## 7. Component Organization

### Modular Structure
Extract reusable components:

- `Header` components for page actions
- `FilterChips` for filter display
- `DataGrid` wrappers for consistent table behavior
- `Dialog` components for modals
- `StatusDialog` for user feedback

## 8. Code Quality Standards

### Required Practices

- Always include try-catch blocks for async operations
- Use consistent naming conventions (camelCase for variables, PascalCase for components)
- Include loading states for all async operations
- Implement proper cleanup in useEffect hooks
- Use TypeScript-style prop validation where possible

### Performance Guidelines

- Use `useCallback` for event handlers passed to child components
- Implement `useMemo` for expensive calculations
- Avoid inline object/array creation in render methods
- Use proper dependency arrays in useEffect hooks

## 9. Security & Authorization

### RBAC Integration

```jsx
const canAccess = (requiredPermission) =>
  canMenuPage(MENUS.LEFT, PAGES.PAGE_NAME, requiredPermission);

useEffect(() => {
  if (rbacRoles != null && rbacRoles.length > 0) {
    if (!canAccess(PERMISSIONS.READ)) {
      router.push("/401");
    }
  }
}, [rbacRoles]);
```

## 10. Testing Guidelines

### Required Tests

- Unit tests for all handler functions
- Integration tests for API calls
- Component rendering tests
- User interaction tests for forms and dialogs

## 11. Handler Function Categories

### Event Handlers

- File upload dialogs: Use `openDialog` and `closeDialog` pattern
- Form submissions: Include validation and error handling
- Button clicks: Implement loading states and user feedback

### API Handlers

- Data fetching: Use consistent error handling with try-catch blocks
- Communication endpoints: Follow `/send-communication/{id}` pattern
- Export operations: Use `responseType: "arraybuffer"` for file downloads

### Form Handlers

- Input changes: Use functional state updates for arrays and objects
- Dynamic lists: Implement add/remove with proper sequence management
- Template selection: Save 'key' property instead of 'value' for SelectAutoComplete

### State Management Handlers

- Filter operations: Use `filter()` method for removals
- Pagination: Reset page to 1 when changing page size
- Dialog states: Include proper cleanup in close handlers

## 12. Memory Patterns

### Communication Features

- For donor group WhatsApp communication, use endpoint pattern '/send-communication/{groupId}' with payload containing communicationModeEnums: 'WHATSAPP' and templateIdOrName fields
- For ContactGroupActions component, implement Email messaging using the same pattern as DonorsPage with emailsList array containing test1-test5 entries and communicationModeEnums: EMAIL
- When implementing similar communication features (WhatsApp, Email, SMS), reuse existing dialog components and handlers, only changing the communicationModeEnums parameter in the API payload

### File Upload Patterns

- Use `openDialog` and `closeDialog` pattern for file preview functionality
- Always include error handling for different file types (PDF, DOCX)
- Use `URL.createObjectURL` for PDF preview and `mammoth` for DOCX conversion

### API Endpoint Patterns

- For donor group WhatsApp communication, use endpoint pattern '/send-communication/{groupId}' with payload containing communicationModeEnums: 'WHATSAPP' and templateIdOrName fields
- For ContactGroupActions component, implement Email messaging using the same pattern as DonorsPage with emailsList array containing test1-test5 entries and communicationModeEnums: EMAIL

## 13. Best Practices Summary

### Development Standards

1. **Consistency**: Follow established patterns across all components
2. **Error Handling**: Always include try-catch blocks and user feedback
3. **Performance**: Use React optimization hooks appropriately
4. **Security**: Implement proper RBAC checks
5. **Testing**: Write comprehensive tests for all functionality

### Code Organization

6. **Documentation**: Keep code well-commented and self-documenting
7. **Modularity**: Extract reusable components and utilities
8. **State Management**: Use functional updates and proper cleanup
9. **API Integration**: Follow consistent request/response patterns
10. **User Experience**: Provide loading states and clear feedback

### Quality Assurance

- Always validate user inputs before processing
- Implement proper loading states for async operations
- Use consistent error messaging across the application
- Follow accessibility guidelines for UI components
- Maintain clean and readable code structure

## 14. Common Patterns Reference

### Template Selection Pattern

```jsx
const handleTemplateChange = (event) => {
  const selectedArray = communicationMode === "EMAIL" ? emailsList : templates;
  const selectedTemplate = selectedArray.find(
    (item) => item.value === event.target.value
  );
  const templateKey = selectedTemplate ? selectedTemplate.key : event.target.value;
  setSelectedTemplateName(templateKey);
};
```

### Dynamic List Management

```jsx
const handleInputChange = (id, field, value) => {
  setData((prevData) =>
    prevData.map((row) => (row.id === id ? { ...row, [field]: value } : row))
  );
};

const handleAddRow = () => {
  setData((prevData) => [
    ...prevData,
    {
      id: crypto.randomUUID(),
      field1: "",
      field2: "",
      sequence: prevData.length + 1,
    },
  ]);
};

const handleDeleteRow = (id) => {
  setData((prevData) => {
    const updatedData = prevData.filter((row) => row.id !== id);
    return updatedData.map((row, index) => ({
      ...row,
      sequence: index + 1,
    }));
  });
};
```

---

**Note**: This document should be referenced for all development work in the Donation Receipt Frontend Application to ensure consistency and maintainability across the codebase.
