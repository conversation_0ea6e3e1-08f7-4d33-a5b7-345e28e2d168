import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
} from "@mui/material";
import Icon from "src/@core/components/icon";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";

const MessageDialog = ({
  open,
  onClose,
  communicationMode,
  templates,
  emailsList,
  selectedTemplateName,
  onTemplateChange,
  onSend,
  sendingMessage,
}) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          width: "400px",
          height: "250px",
        },
      }}
    >
      <DialogTitle
        sx={{
          position: "relative",
          borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
          p: (theme) => `${theme.spacing(1.75, 4)} !important`,
          display: "flex",
          alignItems: "center",
          justifyContent: { xs: "start" },
          fontSize: { xs: 19, md: 20 },
          marginLeft: { xl: 1.5, lg: 1.5, md: 1.5, sm: 1.5, xs: 1.5 },
        }}
        textAlign={"center"}
      >
        Send Message via {communicationMode === "EMAIL" ? "Email" : "WhatsApp"}
      </DialogTitle>

      <Box sx={{ position: "absolute", top: "8px", right: "24px" }}>
        <IconButton
          size="small"
          onClick={onClose}
          sx={{
            borderRadius: 1,
            color: "common.white",
            backgroundColor: "primary.main",
            "&:hover": {
              backgroundColor: "#66BB6A",
              transition: "background 0.5s ease, transform 0.5s ease",
            },
          }}
        >
          <Icon icon="tabler:x" fontSize="1rem" />
        </IconButton>
      </Box>

      <DialogContent>
        <Box sx={{ mt: 2 }}>
          <SelectAutoComplete
            id="template-select"
            label={`Select a ${
              communicationMode === "EMAIL" ? "Email Template" : "Template"
            }`}
            nameArray={communicationMode === "EMAIL" ? emailsList : templates}
            value={
              communicationMode === "EMAIL"
                ? emailsList.find((email) => email.key === selectedTemplateName)
                    ?.value || null
                : templates.find(
                    (template) => template.key === selectedTemplateName
                  )?.value || null
            }
            onChange={onTemplateChange}
          />
        </Box>
      </DialogContent>

      <DialogActions
        sx={{
          justifyContent: "end",
          borderTop: (theme) => `1px solid ${theme.palette.divider}`,
          p: (theme) => `${theme.spacing(2.5)} !important`,
          marginRight: { xl: 4, lg: 3.5, md: 3.5, sm: 3.5, xs: 3.5 },
        }}
      >
        <Button onClick={onClose}>Cancel</Button>
        <Button
          onClick={onSend}
          disabled={!selectedTemplateName || sendingMessage}
          variant="contained"
          color="primary"
        >
          {sendingMessage ? "Sending..." : "Send"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default MessageDialog; 