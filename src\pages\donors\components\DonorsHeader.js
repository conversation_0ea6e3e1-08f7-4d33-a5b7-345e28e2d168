import { Box, Button, Grid, Typography } from "@mui/material";
import FileDownloadSharpIcon from "@mui/icons-material/FileDownloadSharp";
import FileUploadSharpIcon from "@mui/icons-material/FileUploadSharp";

const DonorsHeader = ({ onAdd, onExport, onImport, selectedRows }) => {
  return (
    <Box
      sx={{
        py: 3,
        px: 6,
        rowGap: 2,
        columnGap: 4,
        display: "flex",
        flexWrap: "wrap",
        alignItems: "center",
        justifyContent: "space-between",
      }}
    >
      <Grid container spacing={3} alignItems="center">
        <Grid item xs={12} sm={4}>
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            Donors
          </Typography>
        </Grid>
        <Grid item xs={12} sm={8}>
          <Grid container spacing={2} justifyContent="flex-end">
            <Grid item xs="auto">
              <Button
                variant="contained"
                onClick={onExport}
                startIcon={<FileUploadSharpIcon />}
                disabled={!selectedRows || selectedRows.length === 0}
                sx={{
                  height: 40,
                  minWidth: 80,
                  px: 3,
                  ml: 1,
                  background: "#23236A",
                  color: "#fff",
                  fontWeight: 600,
                  borderRadius: 1,
                  boxShadow: "none",
                  "&:hover": {
                    background: "#1a1a4f",
                    boxShadow: "none",
                  },
                  display: "flex",
                  alignItems: "center",
                }}
              >
                Export
              </Button>
            </Grid>

            <Grid item xs="auto">
              <Button
                variant="contained"
                startIcon={<FileDownloadSharpIcon />}
                onClick={onImport}
                sx={{
                  height: 40,
                  minWidth: 80,
                  px: 3,
                  ml: 1,
                  background: "#23236A",
                  color: "#fff",
                  fontWeight: 600,
                  borderRadius: 1,
                  boxShadow: "none",
                  "&:hover": {
                    background: "#1a1a4f",
                    boxShadow: "none",
                  },
                  display: "flex",
                  alignItems: "center",
                }}
              >
                Import
              </Button>
            </Grid>
            <Grid item xs="auto">
              <Button
                variant="contained"
                onClick={onAdd}
                sx={{
                  height: 40,
                  minWidth: 80,
                  px: 3,
                  ml: 1,
                  background: "#23236A",
                  color: "#fff",
                  fontWeight: 600,
                  borderRadius: 1,
                  boxShadow: "none",
                  "&:hover": {
                    background: "#1a1a4f",
                    boxShadow: "none",
                  },
                  display: "flex",
                  alignItems: "center",
                }}
              >
                Add
              </Button>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Box>
  );
};

export default DonorsHeader; 