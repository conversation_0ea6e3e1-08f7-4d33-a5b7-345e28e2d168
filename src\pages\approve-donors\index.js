import { yupResolver } from "@hookform/resolvers/yup";
import {
  Box,
  CardContent,
  Divider,
  Link,
  Menu,
  MenuItem,
  Tooltip,
} from "@mui/material";
import Button from "@mui/material/Button";
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import { DataGrid } from "@mui/x-data-grid";
import axios from "axios";
import { useContext, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import Icon from "src/@core/components/icon";
import CustomAvatar from "src/@core/components/mui/avatar";
import CustomChip from "src/@core/components/mui/chip";
import FallbackSpinner from "src/@core/components/spinner";
import authConfig from "src/configs/auth";
import {
  Upload,
  Edit as EditIcon,
  Save as SaveIcon,
  Delete as DeleteIcon,
} from "@mui/icons-material";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import { AuthContext } from "src/context/AuthContext";
import DonorsTable from "./DonorsTable";

const userStatusObj = {
  true: "Active",
  false: "Inactive",
  null: "Inactive",
};

const userStatusObj2 = {
  COMPLETED: "Completed",
  PENDING: "Pending",
  PARTIAL: "Partial",
};

const DonorsImport = () => {
  const [userList, setUserList] = useState([]);

  const [allLoading, setAllLoading] = useState(true);

  const [openDialog, setOpenDialog] = useState(false);

  const { stagingDonorId, setStagingDonorId ,stagingDonorDetails,setStagingDonorDetails} = useContext(AuthContext)

  const [searchKeyword, setSearchKeyword] = useState("");

  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [currentRow, setCurrentRow] = useState();
  const [rowCount, setRowCount] = useState(0);

  const {
    register,
    handleSubmit,
    setError,
    clearErrors,
    control,
    reset,
    formState: { errors },
  } = useForm();

  const handleOpenDialog = () => {
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    reset();
    setOpenDialog(false);
  };

  const handleEditDialogClose = () => {
    setStagingDonorDetails(null)
    setEditDialog(false);
    fetchTenants(page, pageSize, searchKeyword);
  };

  const fetchTenants = async (currentPage, currentPageSize, searchKeyword) => {
    const url = getUrl(authConfig.donorImportEndpoint) + "/import-batch/all";

    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      searchKeyword: searchKeyword,
    };

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data) {
        setUserList(response.data?.importBatchResponsesList || []);
        setRowCount(response.data?.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
      setAllLoading(false);
    }
  };

  useEffect(() => {
    fetchTenants(page, pageSize, searchKeyword);
  }, [page, pageSize, searchKeyword]);

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  const [menu, setMenu] = useState(null);

  const handleCloseMenuItems = () => {
    setMenu(null);
  };
  const [editDialog, setEditDialog] = useState(false);

  const mapIsActiveToLabel = (status) => {
    return userStatusObj[status];
  };

  const mapIsActiveToLabel2 = (status) => {
    return userStatusObj2[status];
  };

  const columns = [
    {
      field: "fileName",
      headerName: "File Name",
      flex: 2.5,
      minWidth: 185,
    },
    {
      field: "totalRecords",
      headerName: "No. of Records",
      flex: 1.9,
      minWidth: 160,
    },
    {
      field: "processedRecords",
      headerName: "Processed Records",
      flex: 1.2,
      minWidth: 160,
    },
    {
      field: "status",
      headerName: "Approval Status",
      flex: 1.4,
      minWidth: 115,
      renderCell: ({ row }) => {
        return (
          <CustomChip
            rounded={true}
            skin="light"
            size="small"
            label={mapIsActiveToLabel2(row.status)}
            color={row.status === "PENDING" ? "error" : (row.status === "COMPLETED" ? "success" : "warning") }
            sx={{ textTransform: "capitalize" }}
          />
        );
      },
    },
    {
      field: "isActive",
      headerName: "Status",
      flex: 0.13,
      minWidth: 115,
      renderCell: ({ row }) => {
        return (
          <CustomChip
            rounded={true}
            skin="light"
            size="small"
            label={mapIsActiveToLabel(row.isActive)}
            color={row.isActive ? "success" : "error"}
            sx={{ textTransform: "capitalize" }}
          />
        );
      },
    },
    {
      field: "actions",
      headerName: "Actions",
      width: 100,
      sortable: false,
      renderCell: (params) => {
        const handleClickMenu = (event) => {
          event.stopPropagation();
          const row = params.row;
          setCurrentRow(row);
          setMenu(event.currentTarget);
           setStagingDonorId({
            ...stagingDonorId,
            id: params.row.id,
          });
        };

        const onClickViewProfile = () => {
          setEditDialog(true);
          handleCloseMenuItems();
        };

        // const onClickToggleStatus = () => {
        //   setOpenDeleteDialog(true);
        //   handleCloseMenuItems(); // Close menu after action
        // };

        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <CustomAvatar
              skin="light"
              variant="rounded"
              sx={{
                mr: { xs: 2, lg: 4 },
                width: 34,
                height: 34,
                cursor: "pointer",
              }}
              onClick={handleClickMenu}
            >
              <Icon icon="bi:three-dots-vertical" />
            </CustomAvatar>
            <Menu
              id="actions-menu"
              anchorEl={menu}
              open={Boolean(menu)}
              onClose={handleCloseMenuItems}
            >
              <MenuItem onClick={onClickViewProfile}>Update/Approve</MenuItem>
              {/* <MenuItem>
                {currentRow?.isActive ? "Deactivate" : "Activate"}
              </MenuItem> */}
            </Menu>
          </div>
        );
      },
    },
  ];

  return (
    <>
      <Grid>
        <>
          <Box
            sx={{
              py: 3,
              px: 6,
              rowGap: 2,
              columnGap: 4,
              display: "flex",
              flexWrap: "wrap",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} sm={4} sx={{ textAlign: "flex-start" }}>
                <Typography variant="h6" fontWeight={"600"}>
                  Donors Files
                </Typography>
              </Grid>
            </Grid>
          </Box>

          <DonorsTable
            open={editDialog}
            onClose={handleEditDialogClose}
            donorsList={stagingDonorDetails}
            rowData={currentRow}
          />

          <CardContent>
            <div style={{ height: 380, width: "100%" }}>
              {allLoading ? (
                <Box
                  display="flex"
                  justifyContent="center"
                  alignItems="center"
                  height="60vh"
                >
                  <FallbackSpinner />
                </Box>
              ) : (
                <>
                  <DataGrid
                    rows={userList || []}
                    columns={columns}
                    pagination
                    pageSize={pageSize}
                    page={page - 1}
                    rowsPerPageOptions={rowsPerPageOptions}
                    rowCount={rowCount}
                    paginationMode="server"
                    onPageChange={handlePageChange}
                    onPageSizeChange={handlePageSizeChange}
                    rowHeight={38}
                    headerHeight={38}
                    components={{
                      NoRowsOverlay: () => (
                        <Typography
                          variant="body1"
                          align="center"
                          sx={{ marginTop: "120px" }}
                        >
                          {userList?.length === 0 ? "No Data" : "No Rows"}
                        </Typography>
                      ),
                    }}
                  />
                </>
              )}
            </div>
          </CardContent>
        </>
      </Grid>
    </>
  );
};

export default DonorsImport;
