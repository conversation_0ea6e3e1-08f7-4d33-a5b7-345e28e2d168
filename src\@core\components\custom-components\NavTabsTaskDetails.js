import React, { useContext, useEffect, useState } from "react";
import { Box, Tab } from "@mui/material";
import TabList from "@mui/lab/TabList";
import TabPanel from "@mui/lab/TabPanel";
import TabContext from "@mui/lab/TabContext";
import Typography from "@mui/material/Typography";
import { DataGrid } from "@mui/x-data-grid";
import axios from "axios";
import Tooltip from "@mui/material/Tooltip";
import MenuItem from "@mui/material/MenuItem";
import Menu from "@mui/material/Menu";
import Icon from "src/@core/components/icon";

import { useAuth } from "src/hooks/useAuth";
import { AuthContext } from "src/context/AuthContext";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import authConfig from "src/configs/auth";
import CustomAvatar from "src/@core/components/mui/avatar";
import ViewTask from "src/pages/tasks/ViewTask";
import { useForm } from "react-hook-form";



const NavTabsTaskDetails = () => {

  const {
    reset
  } = useForm();
  const [value, setValue] = useState("");
  const [statusData, setStatusData] = useState([]);
  const [taskList, setTaskList] = useState([]);
  const [pageSize, setPageSize] = useState(10);
  const [page, setPage] = useState(0);
  const [rowCount, setRowCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [menu, setMenu] = useState(null);
  const [employeesData, setEmployeesData] = useState([]);
  const [openViewDialog, setOpenViewDialog] = useState(false);

   // Define columns
   const columns = [
    {
      field: "title",
      minWidth: 95,
      headerName: "Title",
      flex: 0.13,
      valueGetter: (params) => params?.row?.additionalDetails?.title,
    },
    {
      field: "description",
      minWidth: 125,
      headerName: "Description",
      flex: 0.13,
      valueGetter: (params) => params?.row?.additionalDetails?.description,
    },
 
    {
      field: "startDate",
      minWidth: 175,
      headerName: "Target Start Date",
      flex: 0.18,
      valueGetter: (params) => params?.row?.additionalDetails?.targetStartDate,
    },
    {
      field: "dueDate",
      minWidth: 155,
      headerName: "Target End Date",
      flex: 0.18,
      valueGetter: (params) => params?.row?.additionalDetails?.targetEndDate,
    },
    {
      flex: 0.15,
      minWidth: 120,
      field: "assignedTo",
      headerName: "Assigned to",
      renderCell: (params) => {
        const assignedTo = employeesData?.find(
          (item) => item?.id === params?.row?.assignedTo
        );
        return <span>{assignedTo ? assignedTo?.name : ""}</span>;
      },
    },
    {
      flex: 0.13,
      minWidth: 120,
      field: "priorityId",
      headerName: "Priority",
      renderCell: (params) => {
        const priority = listValues?.find(
          (item) => item?.id === params?.row?.additionalDetails?.priorityId
        );
        return <span>{priority ? priority?.name : "Unknown"}</span>;
      },
    },
    {
      flex: 0.077,
      field: "edit",
      headerName: "Actions",
      sortable: false,
      minWidth: 120,
      disableClickEventBubbling: true,
      renderCell: (params) => {
        const onClick = () => {
          const row = params?.row;

          setOpenViewDialog(true);
          setTaskData({
            ...taskData,
            id: row?.id,
          });
        };

        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Tooltip title="Edit or View">
              <CustomAvatar
                skin="light"
                variant="rounded"
                sx={{
                  mr: { xs: 2, lg: 4 },
                  width: 34,
                  height: 34,
                  cursor: "pointer",
                }}
                onClick={onClick}
              >
                <Icon icon="iconamoon:edit" />
              </CustomAvatar>
            </Tooltip>
          </div>
        );
      },
    },
  ].filter(Boolean); // Filter out null values if the condition is false

  const auth = useAuth();
  const {
    getAllListValuesByListNameId,
    listValues,
    taskData,
    setTaskData,
    taskDataDetails,
  } = useContext(AuthContext);

  useEffect(() => {
    getAllListValuesByListNameId(
      authConfig.statusListNamesId,
      (data) => {
        const orderedData = orderStatusData(data?.listValues);
        setStatusData(orderedData);
        if (orderedData.length > 0) {
          setValue(orderedData[0].id.toString()); // Initialize with the first status ID
        }
      },
      (error) => {
        console.error("Failed to load status data:", error);
      }
    );
  }, []);

  useEffect(() => {
    if (value) {
      // Only fetch tasks if a valid status ID is set
      fetchTasks(page, pageSize, "", value);
    }
  }, [value, page, pageSize]); // Ensure that tasks are refetched when tab, page, or pageSize changes

  useEffect(() => {
    // Fetch all employees
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdownNew) + "?selectionType=EMPLOYEES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setEmployeesData(res?.data?.data);
      })
      .catch((err) => console.log("Employees error", err));
  }, []);

  const handleChange = (event, newValue) => {
    setValue(newValue); // Update the current status ID based on tab change
  };

  const handleCloseEditDialog = () => {
    reset();
    setOpenViewDialog(false);
    fetchTasks(page, pageSize, "", value)
  };

 

  const fetchTasks = async (
    currentPage,
    currentPageSize,
    searchKeyword,
    statusId
  ) => {
    setLoading(true);
    const url = getUrl(authConfig.getAllTasks);
    const headers = getAuthorizationHeaders();

    try {
      const response = await axios.post(
        url,
        {
          page: currentPage,
          pageSize: currentPageSize,
          searchKeyword: searchKeyword,
          isAssignedTo: true,
          isCreatedBy: true,
          statusId: statusId, // Make sure this is the updated status ID
        },
        { headers }
      );

      setTaskList(response.data?.tasks || []);
      setRowCount(response.data?.rowCount || 0);
    } catch (error) {
      console.error("Error fetching tasks:", error);
    } finally {
      setLoading(false);
    }
  };

  const orderStatusData = (data) => {
    const defaultOrder = [
      "Yet To Start",
      "Work In Progress",
      "Re-Open",
      "Cancelled",
      "Completed",
      "Closed",
    ];
    return data
      .map((status) => {
        const index =
          defaultOrder.indexOf(status.listValue) !== -1
            ? defaultOrder.indexOf(status.listValue)
            : defaultOrder.length;
        return { ...status, index };
      })
      .sort((a, b) => a.index - b.index);
  };

  const [valueName,setValueName] = useState("")

  useEffect(()=>{
    const name = value ? listValues?.find(item => item?.id === value)?.name : null;
    setValueName(name)
  },[value])

  return (
    <TabContext value={value}>
      <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
        <TabList
          variant="scrollable"
          scrollButtons="auto"
          onChange={handleChange} aria-label="Task Status Tabs">
          {statusData?.map((status) => (
            <Tab
              key={status?.id}
              label={status?.listValue}
              value={status?.id.toString()}
              sx={{ display: 'flex', flexGrow: 1 }}
            />
          ))}
        </TabList>
      </Box>
      <TabPanel value={value}>
        <Box sx={{ height: 380, width: "100%" }}>
          <DataGrid
            rows={taskList}
            rowCount={rowCount}
            columns={columns}
            pageSize={pageSize}
            rowsPerPageOptions={taskList?.length > 0 ? [10,15,20] : []}
            pagination
            paginationMode="server"
            onPageChange={(newPage) => setPage(newPage)}
            onPageSizeChange={(newPageSize) => setPageSize(newPageSize)}
            loading={loading}
            rowHeight={38}
            headerHeight={38}
            // Adjust the size of the checkboxes
            // checkboxSelectionProps={{
            //   style: { width: 14, height: 14 }, 
            // }}
            sx={{
              '& .MuiDataGrid-footerContainer': {
                minHeight: '20px', // Adjust the footer height
                padding: '0px 8px', // Reduce padding inside the footer
              },
              '& .MuiTablePagination-toolbar': {
                minHeight: '20px', // Adjust the pagination control height
              }
            }}
            components={{
              NoRowsOverlay: () => (
                <Typography variant="body1" align="center" sx={{ marginTop: '40px' }}>
                  {value ? `No data in "${valueName}" section` : "No Rows"}
                </Typography>
              ),
            }}
          />
        </Box>
        <ViewTask
          open={openViewDialog}
          onClose={handleCloseEditDialog}
          data={taskDataDetails}
          employeesData={employeesData}
          fetchUsers={fetchTasks}
        />
      </TabPanel>
    </TabContext>
  );
};

export default NavTabsTaskDetails;
