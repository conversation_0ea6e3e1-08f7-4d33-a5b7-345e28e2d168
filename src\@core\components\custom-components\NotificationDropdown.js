import React, { useEffect, useState } from "react";
import {
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Tooltip,
  Paper,
  ClickAwayListener,
  IconButton,
  Drawer,
  Dialog,
  DialogActions,
  Typography,
  Box,
  Button,
  Card,
  Menu,
  MenuItem,
  DialogContent,
  DialogContentText,
  useMediaQuery,
} from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";
import Icon from "src/@core/components/icon";
import { useRouter } from "next/router";
import { useTheme } from "@mui/material/styles";
import axios from "axios";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import authConfig from "src/configs/auth";

// ** Util Import
import { getInitials } from "src/@core/utils/get-initials";

import { styled } from "@mui/material/styles";
import CustomAvatar from "src/@core/components/mui/avatar";
import { NOTIFICATION_REDIRECT_PAGES } from "src/constants";
import MoreVertIcon from "@mui/icons-material/MoreVert";

const Avatar = styled(CustomAvatar)({
  width: 38,
  height: 38,
  fontSize: "1.125rem",
});

const NotificationDropdown = ({
  notifications,
  markAsRead,
  deleteNotification,
  setNotifications,
  fetchNotifications,
  notificationContent,
  closeDropdown,
  openDrawer,
  open,
  handleDrawerClose,
  createdOnDate,
  notificationData,
  markAsReadV2,
}) => {
  // Define a fixed width for the notification container and the content's fixed height
  const containerWidth = "300px"; // Adjust the width as needed
  const contentHeight = "40px"; // Adjust the height as needed

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const isTablet = useMediaQuery(theme.breakpoints.down("md"));

  const [hoveredCard, setHoveredCard] = useState(null);
  const [openDialogContent, setOpenDialogContent] = useState(false);

  const [dialogMessage, setDialogMessage] = useState("");
  const [menuAnchor, setMenuAnchor] = useState(null);
  const [selectedNotificationId, setSelectedNotificationId] = useState(null);
  const router = useRouter();

  const [windowWidth, setWindowWidth] = useState(window.innerWidth);

  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const cardStyle = (id) => ({
    maxWidth: "440px", // Use maxWidth to ensure it does not exceed the drawer's width
    height: "50%",
    margin: "1.2 auto",
  });

  // Style for ListItemText to increase font size
  const listItemTextStyle = {
    fontFamily: "Lato, sans-serif",
    fontSize: "15px",
    fontWeight: "normal",
    fontStyle: "normal",
    whiteSpace: "normal",
    WebkitBoxOrient: "vertical",
    lineHeight: "1.5", // Adjust line height to ensure lines are spaced nicely
    marginBottom: "12px",
  };

  const listItemStyle = {
    backgroundColor: "white",
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    minHeight: "70px",
    padding: windowWidth < 426 ? "5px 5px" : "10px 15px",
  };

  const listItemStyleDynamic = (windowWidth) => ({
    ...listItemStyle,
    padding: windowWidth < 426 ? "5px 5px" : "10px 15px", // Adjust padding based on screen width
  });

  const listItemTextStyleDynamic = (windowWidth) => ({
    ...listItemTextStyle,
    fontSize: windowWidth < 426 ? "14px" : "15px", // Adjust font size for small screens
    WebkitLineClamp: windowWidth < 426 ? 3 : 2, // Allow more text lines on smaller screens if needed
  });

  const deleteIconStyle = (id) => ({
    visibility: hoveredCard === id ? "visible" : "hidden",
    alignSelf: "center",
    flexShrink: 0, // Prevent the icon from shrinking
  });

  const markAllAsRead = () => {
    axios({
      method: "patch",
      url: getUrl(authConfig.notificationV2Endpoint) + "/mark/all",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setNotifications((prevNotifications) =>
          prevNotifications.map((notification) => ({
            ...notification,
            isRead: true,
          }))
        );
      })
      .catch((err) => console.error("Error marking all as read:", err));
  };

  const handleClearAllNotifications = ({}) => {
    if (!Array.isArray(notifications)) {
      console.error("handleClearAll expects an array of notifications");
      return;
    }
    const deletePromises = notifications.map(() => {
      return axios({
        method: "DELETE",
        url: getUrl(authConfig.notificationV2Endpoint) + "/all",
        headers: getAuthorizationHeaders(),
      });
    });
    Promise.all(deletePromises)
      .then((results) => {
        setNotifications([]); // Clear all notifications after successful deletion
        setOpenDialogContent(false);
        fetchNotifications(); // Refetch notifications, if necessary
      })
      .catch((err) => {
        setOpenDialogContent(false);
        console.error("An error occurred while deleting notifications", err);
      });
  };

  const handleClearAll = ({}) => {
    setOpenDialogContent(true);
    const message = `
    <div>
    <h3><b>Clear All Notifications</b></h3>
    <h4>
      By proceeding, you will permanently erase all notifications. This action cannot be undone. Please confirm if you wish to continue. 
    </h4>
    </div>
    `;
    setDialogMessage(message);
  };

  const handleClose = () => {
    setOpenDialogContent(false);
  };

  const handleMenuOpen = (event, notificationId) => {
    setMenuAnchor(event.currentTarget);
    setSelectedNotificationId(notificationId);
  };

  const handleMenuClose = () => {
    setMenuAnchor(null);
    setSelectedNotificationId(null);
  };

  const handleMarkAsRead = () => {
    if (selectedNotificationId) {
      markAsReadV2(selectedNotificationId);
    }
    handleMenuClose();
  };

  const handleDelete = () => {
    if (selectedNotificationId) {
      deleteNotification(selectedNotificationId);
    }
    handleMenuClose();
  };

  const RenderAvatar = ({ notification }) => {
    const { avatarColor } = notification;

    const getAbbreviatedName = (name) => {
      const words = name?.split(" ");
      if (words && words.length > 2) {
        return `${words[0]} ${words[1]}`;
      }
      return name;
    };

    return (
      <Avatar
        skin="light"
        color={avatarColor}
        sx={{
          fontSize:
            notification?.createdBy && notification.createdBy.length == 1
              ? "36px"
              : "20px",
          width: 45,
          height: 45,
        }}
      >
        {getInitials(getAbbreviatedName(notification?.createdBy))}
      </Avatar>
    );
  };

  function formatDate(dateString) {
    const date = new Date(dateString);
    const options = { year: "numeric", month: "long", day: "numeric" };
    return date.toLocaleDateString("en-US", options); // Format the date to "December 27, 2023"
  }

  function formatCreatedOnDate(dateString) {
    const options = {
      year: "2-digit",
      month: "2-digit",
      day: "2-digit",
      hour: "numeric",
      minute: "numeric",
      hour12: true,
    };
    const date = new Date(dateString);
    return date.toLocaleString("en-US", options);
  }

  return (
    <>
      <Card>
        <Box sx={{ display: "flex", flexDirection: "row" }}>
          <Drawer
            open={open}
            anchor="right"
            variant="temporary"
            onClose={closeDropdown}
            ModalProps={{ keepMounted: true }}
            sx={{
              "& .MuiDrawer-paper": {
                width: { xs: "80%", sm: 440 }, // Adjust the width for tablet view (md)
                height: "100%",
                display: "flex",
                flexDirection: "column",
                width: isMobile
                  ? "80% !important"
                  : isTablet
                  ? "50% !important"
                  : 440, // Adjust width for tablet view
              },
            }}
          >
            <Box
              sx={{
                flex: "0 1 auto",
                position: "sticky",
                top: 3,
                zIndex: 1,
                backgroundColor: "white",
              }}
            >
              <Typography
                sx={{
                  position: "relative",
                  borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
                  p: (theme) => `${theme.spacing(1.75, 4)} !important`,
                  padding: "10px 20px",
                  display: "flex",
                  fontSize: 20,
                  backgroundColor: "white",
                  fontWeight: "Medium",
                  top: 0,
                  left: 0,
                  right: 0,
                }}
              >
                Notifications
                <Box
                  sx={{
                    position: "absolute",
                    top: "4px",
                    right: "14px",
                    display: "flex",
                    gap: "8px",
                  }}
                >
                  <IconButton
                    size="small"
                    onClick={(event) => setMenuAnchor(event.currentTarget)}
                    sx={{
                      borderRadius: 1,
                      color: "common.white",
                      backgroundColor: "primary.main",
                      "&:hover": {
                        backgroundColor: "primary.main",
                      },
                    }}
                  >
                    <MoreVertIcon fontSize="1rem" />
                  </IconButton>
                  <IconButton
                    size="small"
                    onClick={closeDropdown}
                    sx={{
                      borderRadius: 1,
                      color: "common.white",
                      backgroundColor: "primary.main",
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                      "&:hover": {
                        backgroundColor: "primary.main",
                        transition: "background 0.5s ease, transform 0.5s ease",
                      },
                    }}
                  >
                    <Icon
                      icon="tabler:x"
                      fontSize="1rem" // Adjust the size for better visibility
                      style={{
                        display: "inline-block",
                        verticalAlign: "middle",
                      }}
                    />
                  </IconButton>
                  <Menu
                    anchorEl={menuAnchor}
                    open={Boolean(menuAnchor)}
                    onClose={() => setMenuAnchor(null)}
                  >
                    <MenuItem
                      onClick={() => {
                        markAllAsRead();
                        setMenuAnchor(null);
                      }}
                    >
                      Mark All as Read
                    </MenuItem>
                  </Menu>
                </Box>
              </Typography>
            </Box>
            <Box
              sx={{
                flex: "1 1 auto",
                overflowY: "auto",
                p: 2,
              }}
            >
              {notifications.length > 0 ? (
                <List>
                  {notifications.map((notification) => (
                    <Card
                      key={notification.notificationId}
                      style={cardStyle(notification.notificationId)}
                      onMouseEnter={() =>
                        setHoveredCard(notification.notificationId)
                      }
                      onMouseLeave={() => setHoveredCard(null)}
                    >
                      <ListItem
                        button
                        onClick={() => markAsRead(notification?.notificationId)}
                        style={{
                          ...listItemStyleDynamic(windowWidth),
                          backgroundColor: notification.isRead
                            ? "white"
                            : "#f2f2f2",
                          cursor: notification.isRead ? "default" : "pointer",
                        }}
                      >
                        <RenderAvatar notification={notification} />
                        <Box
                          style={{
                            flex: 1,
                            marginLeft: "12px",
                            overflow: "hidden",
                            marginRight: "40px",
                          }}
                        >
                          <ListItemText
                            primary={notification.level1Content}
                            primaryTypographyProps={{
                              style: listItemTextStyleDynamic(windowWidth),
                            }}
                          />
                          <Typography
                            variant="body2"
                            color="textSecondary"
                            sx={{
                              paddingTop: "4px",
                              fontSize: {
                                xs: "0.7rem",
                                lg: "0.8rem",
                              },
                            }}
                          >
                            {notification?.createdOn} •{" "}
                            {notification?.createdBy}
                          </Typography>
                        </Box>
                        <ListItemSecondaryAction style={{ marginRight: "5px" }}>
                          <IconButton
                            edge="end"
                            onClick={(event) =>
                              handleMenuOpen(
                                event,
                                notification?.notificationId
                              )
                            }
                          >
                            <MoreVertIcon />
                          </IconButton>
                          <Menu
                            anchorEl={menuAnchor}
                            open={
                              Boolean(menuAnchor) &&
                              selectedNotificationId ===
                                notification?.notificationId
                            }
                            onClose={handleMenuClose}
                          >
                            <MenuItem onClick={handleMarkAsRead}>
                              {notifications.find(
                                (n) =>
                                  n.notificationId === selectedNotificationId
                              )?.isRead
                                ? "Mark as Unread"
                                : "Mark as Read"}
                            </MenuItem>

                            <MenuItem onClick={handleDelete}>Delete</MenuItem>
                          </Menu>
                        </ListItemSecondaryAction>
                      </ListItem>
                    </Card>
                  ))}
                </List>
              ) : (
                <div style={{ textAlign: "center", margin: "20px" }}>
                  No Notifications to Show
                </div>
              )}
            </Box>
            <Box
              sx={{
                position: "fixed",
                height: "60px",
                bottom: 0,
                right: 0,
                borderTop: (theme) => `1px solid ${theme.palette.divider}`,
                p: (theme) => theme.spacing(2),
                display: "flex",
                alignItems: "center",
                backgroundColor: "background.paper",
                width: "auto",
                "@media screen and (max-width: 600px)": {
                  width: "80%", // 80% of screen width on mobile
                  right: "", // Center it horizontally
                },
                "@media screen and (min-width: 601px)": {
                  width: 440,
                },
                width: isMobile
                  ? "80% !important"
                  : isTablet
                  ? "50% !important"
                  : 440,
              }}
            >
              <Typography
                variant="body1"
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "flex-end",
                  cursor: notifications.length > 0 ? "pointer" : "default",
                  "&:hover": {
                    textDecoration:
                      notifications.length > 0 ? "underline" : "none",
                  },
                }}
                onClick={
                  notifications.length > 0
                    ? () => handleClearAll({ notifications })
                    : undefined
                }
              >
                <IconButton
                  onClick={
                    notifications.length > 0
                      ? () => handleClearAll({ notifications })
                      : undefined
                  }
                  sx={{
                    ml: 1,
                    pointerEvents: notifications.length > 0 ? "auto" : "none",
                  }}
                >
                  <DeleteIcon />
                </IconButton>
                Clear all notification(s)
              </Typography>
            </Box>
          </Drawer>

          <Drawer
            open={openDrawer}
            anchor="right"
            variant="temporary"
            onClose={handleDrawerClose}
            ModalProps={{ keepMounted: true }}
            BackdropProps={{
              style: {
                backgroundColor: "rgba(0, 0, 0, 0)", // Ensure the backdrop color is correct
              },
            }}
            sx={{
              "& .MuiDrawer-paper": {
                left: {
                  xs: "0%",
                  sm: "-58%",
                  lg: "8.4%",
                  md: "-25.9%",
                  xl: "14.05%",
                }, // Adjust to stick to the first drawer
                width: { xs: "80%", sm: "51%", md: "40%", lg: "440px" },
                height: { xs: "100%", sm: "100%", md: "100%" },
                overflow: "hidden", // Prevent content overflow
                marginLeft: "440px", // Adjust dynamically based on the width of the first drawer
              },
            }}
          >
            {/* Sticky Header */}
            <Box
              sx={{
                position: "sticky",
                top: 0,
                zIndex: 1,
                backgroundColor: "background.paper",
                borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
                p: (theme) => theme.spacing(2, 3), // Padding for header
              }}
            >
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 600,
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  fontSize: { xs: "1rem", md: "1.25rem" }, // Responsive font size
                }}
              >
                <Typography variant="h6" gutterBottom>
                  {notificationData?.level1Content || ""}
                </Typography>
                <IconButton
                  size="small"
                  onClick={handleDrawerClose}
                  sx={{
                    borderRadius: 1,
                    color: "common.white",
                    backgroundColor: "primary.main",
                    "&:hover": {
                      backgroundColor: "primary.main",
                      transition: "background 0.3s ease, transform 0.3s ease",
                    },
                  }}
                >
                  <Icon icon="tabler:x" fontSize="1rem" />
                </IconButton>
              </Typography>
            </Box>

            {/* Content */}
            <Box
              sx={{
                p: 3,
                overflowY: "auto", // Enable scrolling if content overflows
                height: "calc(100% - 64px)", // Adjust based on header height
              }}
            >
              <Typography
                variant="body1"
                sx={{
                  color: "text.primary",
                  fontSize: { xs: "0.9rem", md: "1rem" }, // Responsive font size
                  lineHeight: 1.6, // Improve readability
                  wordBreak: "break-word", // Handle long text gracefully
                }}
              >
                <Box sx={{ p: 3 }}>
                  <Button
                    variant="contained"
                    color="primary"
                    size="small"
                    sx={{ mt: 2 }}
                    onClick={() => {
                      // Close drawer and dropdown
                      handleDrawerClose();
                      closeDropdown();

                      const redirectPage = notificationData?.redirectPage;
                      const level2Content = notificationData?.level2Content;

                      const pathname =
                        NOTIFICATION_REDIRECT_PAGES[redirectPage];

                      if (pathname) {
                        // Initialize the query object
                        const updatedQuery = {
                          id: level2Content,
                        };

                        if (
                          redirectPage === "SP_LEAD_SNAPSHOT" ||
                          redirectPage === "CHS_LEAD_SNAPSHOT"
                        ) {
                          updatedQuery.tab = "snapshots";

                          // Navigate to the target route
                          router.replace({
                            pathname,
                            query: updatedQuery,
                          });
                        } else {
                          // Remove the tab parameter from updatedQuery
                          const { tab, ...queryWithoutTab } = updatedQuery;

                          // Navigate to the target route with only id in query
                          router.replace({
                            pathname,
                            query: queryWithoutTab,
                          });
                        }
                      } else {
                        console.error(
                          "Navigation failed: Invalid notification type",
                          redirectPage
                        );
                      }
                    }}
                  >
                    Go to Page
                  </Button>
                </Box>
              </Typography>
            </Box>
          </Drawer>
        </Box>
        <Dialog
          open={openDialogContent}
          onClose={handleClose}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              backgroundColor: (theme) => theme.palette.primary.background,
            },
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary.main",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.main"
              >
                <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
              </DialogContentText>
            </DialogContent>
            <DialogActions sx={{ justifyContent: "flex-end" }}>
              <Button
                variant="outlined"
                onClick={handleClose}
                sx={{ width: 100 }}
              >
                Cancel
              </Button>
              <Button
                variant="contained"
                onClick={() => handleClearAllNotifications({ notifications })}
                sx={{ ml: 2, width: 130 }}
              >
                Clear ALL
              </Button>
            </DialogActions>
          </Box>
        </Dialog>
      </Card>
    </>
  );
};

export default NotificationDropdown;
