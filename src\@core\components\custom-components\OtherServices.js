// ** MUI Imports
import Typography from "@mui/material/Typography";
import { useState } from "react";

import { useTheme } from "@emotion/react";

// ** Demo
import AccordionBasic from "src/@core/components/custom-components/AccordionBasic";

// ** Styled Component
import { Table, TableBody, TableContainer, TableRow } from "@mui/material";
import MUITableCell from "src/pages/SP/MUITableCell";
import OtherServicesEdit from "./OtherServicesEdit";
import { useRBAC } from "src/pages/permission/RBACContext";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const OtherServices = ({ data,expanded,readPermission,permission }) => {


  const { can } = useRBAC();
  // ** Hook
  const theme = useTheme();

  const [state2, setState2] = useState("view");

  const viewClick2 = () => {
    setState2("edit");
  };

  const editClick2 = () => {
    setState2("view");
  };

  return (
    <>
      {/* {can(readPermission) &&  */}
      <AccordionBasic
      id={"basic-schema-1"}
      ariaControls={"basic-schema-1"}
      heading={"Other Services"}
      body={
        <>
          {state2 === "view" && (
            <TableContainer
            sx={{ padding:'4px 6px' }}
              className="tableBody"
              //onClick={can(permission) ? viewClick2 : null}
              onClick={ viewClick2}
            >
              <Table>
              <TableBody sx={{ '& .MuiTableCell-root': { p: `${theme.spacing(1.35, 1.125)} !important` } }}>
                  <TableRow>
                    <MUITableCell>
                      <Typography style={field} >Other Services</Typography>
                    </MUITableCell>
                    <MUITableCell>
                      <Typography className="data-field" >
                        {data?.otherServices}
                      </Typography>
                    </MUITableCell>
                  </TableRow>
                  
                </TableBody>
              </Table>
            </TableContainer>
          )}
          {state2 === "edit" && (
            <OtherServicesEdit formData={data} onCancel={editClick2} />
          )}
        </>
      }
      expanded={expanded}
    /> 
      {/* } */}
    </>
  );
};
export default OtherServices;
