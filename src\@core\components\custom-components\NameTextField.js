import React, { forwardRef } from "react";
import { TextField } from "@mui/material";

const NameTextField = forwardRef(({ onChange, ...props }, ref) => {
  const handleChange = (event) => {
    const newValue = event.target.value.replace(/(^\s+|[^a-zA-Z\s])/g, "");
    if (onChange) onChange(newValue);
  };

  return (
    <TextField
      type="text"
      {...props}
      inputRef={ref}
      onChange={handleChange}
    />
  );
});

export default NameTextField;
