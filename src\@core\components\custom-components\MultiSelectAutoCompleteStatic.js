import React, { useState, useEffect } from 'react';
import Box from '@mui/material/Box';
import CustomAutocomplete from './CustomAutoComplete';
import CustomTextField from './CustomTextField';

const MultiSelectAutoCompleteStatic = (props) => {
  const { id, label, nameArray, value, onChange } = props;

  const [selectedValues, setSelectedValues] = useState(value || []);
  const [isDropdownOpen, setDropdownOpen] = useState(false);

  // Sync the selected values with the incoming prop value
  useEffect(() => {
    setSelectedValues(value || []);
  }, [value]);

  // Handles when an option is selected from the autocomplete dropdown
  const handleOptionSelect = (event, newValue) => {
    setSelectedValues(newValue);
    onChange(newValue);  // Trigger the parent onChange
  };

  // Handles removing a selected option
  const handleRemoveSelectedOption = (optionToRemove) => {
    const newSelectedValues = selectedValues.filter(option => option.value !== optionToRemove.value);
    setSelectedValues(newSelectedValues);
    onChange(newSelectedValues);  // Update parent with new selected values
  };

  const handleDropdownOpen = () => {
    setDropdownOpen(true);
  };

  const handleDropdownClose = () => {
    setDropdownOpen(false);
  };

  return (
    <Box sx={{ position: 'relative', zIndex: 1 }}>
      <CustomAutocomplete
        multiple
        autoHighlight
        id={id}
        options={nameArray?.filter(option => !selectedValues?.some(selected => selected.value === option.value))} // Filter out already selected options
        getOptionLabel={(option) => option.key || ''} // Show the key as the label in the dropdown
        value={selectedValues}
        onChange={(event, newValue) => handleOptionSelect(event, newValue)}
        onOpen={handleDropdownOpen}
        onClose={handleDropdownClose}
        open={isDropdownOpen}
        renderOption={(props, option) => (
          <Box
            component='li'
            {...props}
            onClick={(event) => handleOptionSelect(event, [...selectedValues, option])}
          >
            {option.key}  {/* Display key in the list */}
          </Box>
        )}
        renderInput={(params) => (
          <CustomTextField
            {...params}
            size='small'
            placeholder={label}
            sx={{ borderRadius: 1 }}
            inputProps={{
              ...params.inputProps,
            }}
          />
        )}
      />
    </Box>
  );
};

export default MultiSelectAutoCompleteStatic;
