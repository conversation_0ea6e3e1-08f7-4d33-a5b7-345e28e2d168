import { useState, useContext, useEffect } from "react";

// ** MUI Imports
import Grid from "@mui/material/Grid";
import Button from "@mui/material/Button";
import FormControl from "@mui/material/FormControl";
import { Divider } from "@mui/material";
import { AuthContext } from "src/context/AuthContext";

// ** Third Party Imports
import * as yup from "yup";
import { useF<PERSON>, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { FormControlLabel, Switch, Typography } from "@mui/material";

const defaultValues = {
  email: "",
  lastName: "",
  password: "",
  firstName: "",
};

const showErrors = (field, valueLen, min) => {
  if (valueLen === 0) {
    return `${field} field is required`;
  } else if (valueLen > 0 && valueLen < min) {
    return `${field} must be at least ${min} characters`;
  } else {
    return "";
  }
};

const schema = yup.object().shape({
  email: yup.string().email().required(),
  lastName: yup
    .string()
    .min(3, (obj) => showErrors("lastName", obj.value.length, obj.min))
    .required(),
  password: yup
    .string()
    .min(8, (obj) => showErrors("password", obj.value.length, obj.min))
    .required(),
  firstName: yup
    .string()
    .min(3, (obj) => showErrors("firstName", obj.value.length, obj.min))
    .required(),
});

const PretenderingTab = ({
  onCancel,
  setter,
  defaultData,
  setUnsavedChanges,
}) => {
  const { updateEntityServices } = useContext(AuthContext);

  // ** States
  const [state, setState] = useState({
    password: "",
    showPassword: false,
  });

  // ** Hook
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues,
    mode: "onChange",
    resolver: yupResolver(schema),
  });
  const [formData, setFormData] = useState(defaultData);
  const [fsiCalculationsSupported, setFsiCalculationsSupported] = useState(
    defaultData?.fsiCalculations?.isSupported
  );

  const [dataAndDocumentationSupported, setDataAndDocumentationSupported] =
    useState(defaultData?.dataAndDocumentation?.isSupported);

  const [siteAnalysisReportSupported, setSiteAnalysisReportSupported] =
    useState(defaultData?.siteAnalysisReport?.isSupported);

  const [marketAnalysisSupported, setMarketAnalysisSupported] = useState(
    defaultData?.marketAnalysis?.isSupported
  );

  const [feasibilityReportSupported, setFeasibilityReportSupported] = useState(
    defaultData?.feasibilityReport?.isSupported
  );

  const [financialCOPMOFSupported, setFinancialCOPMOFSupported] = useState(
    defaultData?.financialCOPMOF?.isSupported
  );

  const [cashFlowsSupported, setCashFlowsSupported] = useState(
    defaultData?.cashFlows?.isSupported
  );

  const [technicalFeasibilitySupported, setTechnicalFeasibilitySupported] =
    useState(defaultData?.technicalFeasibility?.isSupported);

  const [deemedConveyanceSupported, setDeemedConveyanceSupported] = useState(
    defaultData?.deemedConveyance?.isSupported
  );

  const [areaCorrectionSupported, setAreaCorrectionSupported] = useState(
    defaultData?.areaCorrection?.isSupported
  );

  const [plotSubdivisionSupported, setPlotSubdivisionSupported] = useState(
    defaultData?.plotSubdivision?.isSupported
  );

  const [cTSofficeFollowupSupported, setCTSofficeFollowupSupported] = useState(
    defaultData?.ctsOffice?.isSupported
  );

  const [bMCfileApprovalsSupported, setBMCfileApprovalsSupported] = useState(
    defaultData?.bmcFileApprovals?.isSupported
  );

  const [dPRemark2034Supported, setDPRemark2034Supported] = useState(
    defaultData?.dpRemark2034?.isSupported
  );

  const [searchReport30yearsSupported, setSearchReport30yearsSupported] =
    useState(defaultData?.searchReport30years?.isSupported);

  const [conveyanceDeedSupported, setConveyanceDeedSupported] = useState(
    defaultData?.conveyanceDeed?.isSupported
  );

  const [plotPhysicalSurveySupported, setPlotPhysicalSurveySupported] =
    useState(defaultData?.plotPhysicalSurvey?.isSupported);

  const [
    superimposePlotSurveyCTSplanSupported,
    setSuperimposePlotSurveyCTSplanSupported,
  ] = useState(defaultData?.superImposePlotSurvey?.isSupported);

  const [
    individualFlatMeasurementSupported,
    setIndividualFlatMeasurementSupported,
  ] = useState(defaultData?.individualFlatMeasurement?.isSupported);

  const [a79ProcessSupported, setA79ProcessSupported] = useState(
    defaultData?.a79Process?.isSupported
  );

  const [selectAll, setSelectAll] = useState(false);

  const handleSelectAll = (event) => {
    const { checked } = event.target;

    setFsiCalculationsSupported(checked);
    setDataAndDocumentationSupported(checked);
    setSiteAnalysisReportSupported(checked);
    setMarketAnalysisSupported(checked);
    setFeasibilityReportSupported(checked);
    setFinancialCOPMOFSupported(checked);
    setCashFlowsSupported(checked);
    setTechnicalFeasibilitySupported(checked);
    setDeemedConveyanceSupported(checked);
    setAreaCorrectionSupported(checked);
    setPlotSubdivisionSupported(checked);
    setCTSofficeFollowupSupported(checked);
    setBMCfileApprovalsSupported(checked);
    setDPRemark2034Supported(checked);
    setSearchReport30yearsSupported(checked);
    setConveyanceDeedSupported(checked);
    setPlotPhysicalSurveySupported(checked);
    setSuperimposePlotSurveyCTSplanSupported(checked);
    setIndividualFlatMeasurementSupported(checked);
    setA79ProcessSupported(checked);
    setSelectAll(checked);

    setFormData((prev) => ({
      ...prev,
      fsiCalculations: { ...prev.fsiCalculations, isSupported: checked },
      dataAndDocumentation: {
        ...prev.dataAndDocumentation,
        isSupported: checked,
      },
      siteAnalysisReport: { ...prev.siteAnalysisReport, isSupported: checked },
      marketAnalysis: { ...prev.marketAnalysis, isSupported: checked },
      feasibilityReport: { ...prev.feasibilityReport, isSupported: checked },
      financialCOPMOF: { ...prev.financialCOPMOF, isSupported: checked },
      cashFlows: { ...prev.cashFlows, isSupported: checked },
      technicalFeasibility: {
        ...prev.technicalFeasibility,
        isSupported: checked,
      },
      deemedConveyance: { ...prev.deemedConveyance, isSupported: checked },
      areaCorrection: { ...prev.areaCorrection, isSupported: checked },
      plotSubdivision: { ...prev.plotSubdivision, isSupported: checked },
      ctsOffice: { ...prev.ctsOffice, isSupported: checked },
      bmcFileApprovals: { ...prev.bmcFileApprovals, isSupported: checked },
      dpRemark2034: { ...prev.dpRemark2034, isSupported: checked },
      searchReport30years: {
        ...prev.searchReport30years,
        isSupported: checked,
      },
      conveyanceDeed: { ...prev.conveyanceDeed, isSupported: checked },
      plotPhysicalSurvey: { ...prev.plotPhysicalSurvey, isSupported: checked },
      superImposePlotSurvey: {
        ...prev.superImposePlotSurvey,
        isSupported: checked,
      },
      individualFlatMeasurement: {
        ...prev.individualFlatMeasurement,
        isSupported: checked,
      },
      a79Process: { ...prev.a79Process, isSupported: checked },
    }));

    setUnsavedChanges(true);
    setIsCheckboxChanged(true);
  };

  useEffect(() => {
    if (
      fsiCalculationsSupported &&
      dataAndDocumentationSupported &&
      siteAnalysisReportSupported &&
      marketAnalysisSupported &&
      feasibilityReportSupported &&
      financialCOPMOFSupported &&
      cashFlowsSupported &&
      technicalFeasibilitySupported &&
      deemedConveyanceSupported &&
      areaCorrectionSupported &&
      plotSubdivisionSupported &&
      cTSofficeFollowupSupported &&
      bMCfileApprovalsSupported &&
      dPRemark2034Supported &&
      searchReport30yearsSupported &&
      conveyanceDeedSupported &&
      plotPhysicalSurveySupported &&
      superimposePlotSurveyCTSplanSupported &&
      individualFlatMeasurementSupported &&
      a79ProcessSupported
    ) {
      setSelectAll(true);
    } else {
      setSelectAll(false);
    }
  }, [
    fsiCalculationsSupported,
    dataAndDocumentationSupported,
    siteAnalysisReportSupported,
    marketAnalysisSupported,
    feasibilityReportSupported,
    financialCOPMOFSupported,
    cashFlowsSupported,
    technicalFeasibilitySupported,
    deemedConveyanceSupported,
    areaCorrectionSupported,
    plotSubdivisionSupported,
    cTSofficeFollowupSupported,
    bMCfileApprovalsSupported,
    dPRemark2034Supported,
    searchReport30yearsSupported,
    conveyanceDeedSupported,
    plotPhysicalSurveySupported,
    superimposePlotSurveyCTSplanSupported,
    individualFlatMeasurementSupported,
    a79ProcessSupported,
  ]);

  const [isCheckboxChanged, setIsCheckboxChanged] = useState(false);

  const handleCheckboxChange = (event) => {
    setIsCheckboxChanged(true);
    handleOnChange(event);
  };

  const handleOnChange = (event) => {
    const { name, checked } = event.target;

    switch (name) {
      case "fsiCalculations":
        setFormData({
          ...formData,
          fsiCalculations: {
            ...formData.fsiCalculations,
            isSupported: checked,
          },
        });
        break;
      case "dataAndDocumentation":
        setFormData({
          ...formData,
          dataAndDocumentation: {
            ...formData.dataAndDocumentation,
            isSupported: checked,
          },
        });
        break;
      case "siteAnalysisReport":
        setFormData({
          ...formData,
          siteAnalysisReport: {
            ...formData.siteAnalysisReport,
            isSupported: checked,
          },
        });
        break;
      case "marketAnalysis":
        setFormData({
          ...formData,
          marketAnalysis: { ...formData.marketAnalysis, isSupported: checked },
        });
        break;
      case "feasibilityReport":
        setFormData({
          ...formData,
          feasibilityReport: {
            ...formData.feasibilityReport,
            isSupported: checked,
          },
        });
        break;
      case "financialCOPMOF":
        setFormData({
          ...formData,
          financialCOPMOF: {
            ...formData.financialCOPMOF,
            isSupported: checked,
          },
        });
        break;
      case "cashFlows":
        setFormData({
          ...formData,
          cashFlows: { ...formData.cashFlows, isSupported: checked },
        });
        break;
      case "technicalFeasibility":
        setFormData({
          ...formData,
          technicalFeasibility: {
            ...formData.technicalFeasibility,
            isSupported: checked,
          },
        });
        break;
      case "deemedConveyance":
        setFormData({
          ...formData,
          deemedConveyance: {
            ...formData.deemedConveyance,
            isSupported: checked,
          },
        });
        break;
      case "areaCorrection":
        setFormData({
          ...formData,
          areaCorrection: {
            ...formData.areaCorrection,
            isSupported: checked,
          },
        });
        break;
      case "plotSubdivision":
        setFormData({
          ...formData,
          plotSubdivision: {
            ...formData.plotSubdivision,
            isSupported: checked,
          },
        });
        break;
      case "ctsOffice":
        setFormData({
          ...formData,
          ctsOffice: {
            ...formData.ctsOffice,
            isSupported: checked,
          },
        });
        break;
      case "bmcFileApprovals":
        setFormData({
          ...formData,
          bmcFileApprovals: {
            ...formData.bmcFileApprovals,
            isSupported: checked,
          },
        });
        break;
      case "dpRemark2034":
        setFormData({
          ...formData,
          dpRemark2034: {
            ...formData.dpRemark2034,
            isSupported: checked,
          },
        });
        break;
      case "searchReport30years":
        setFormData({
          ...formData,
          searchReport30years: {
            ...formData.searchReport30years,
            isSupported: checked,
          },
        });
        break;
      case "conveyanceDeed":
        setFormData({
          ...formData,
          conveyanceDeed: {
            ...formData.conveyanceDeed,
            isSupported: checked,
          },
        });
        break;
      case "superImposePlotSurvey":
        setFormData({
          ...formData,
          superImposePlotSurvey: {
            ...formData.superImposePlotSurvey,
            isSupported: checked,
          },
        });
        break;
      case "individualFlatMeasurement":
        setFormData({
          ...formData,
          individualFlatMeasurement: {
            ...formData.individualFlatMeasurement,
            isSupported: checked,
          },
        });
        break;
      case "a79Process":
        setFormData({
          ...formData,
          a79Process: {
            ...formData.a79Process,
            isSupported: checked,
          },
        });
        break;
      case "plotPhysicalSurvey":
        setFormData({
          ...formData,
          plotPhysicalSurvey: {
            ...formData.plotPhysicalSurvey,
            isSupported: checked,
          },
        });
        break;
    }
    setUnsavedChanges(true);
    setIsCheckboxChanged(true);
  };

  async function onSubmit() {
    console.log("FORM DATA:", formData);
    setUnsavedChanges(false);
    const response = await updateEntityServices(
      { preAgreement: formData },
      () => {
        console.error("PreAgreement Details failed");
      }
    );

    if (response) {
      setFormData(response);
      setter(response);
    }

    console.log("Submitted data from Section3:", response);
    onCancel();
  }

  return (
    <>
      <Grid container sx={{ display: "flex", alignItems: "center" }}>
        <Grid item xs={8} sm={4}>
          <FormControl fullWidth>
            <Typography sx={{ fontWeight: 600 }}>Service Name:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={4} sm={2}>
          <Typography sx={{ fontWeight: 600 }}>(Yes / No):</Typography>
        </Grid>

        <Grid item xs={12}>
          <Divider sx={{ my: (theme) => `${theme.spacing(2)} !important` }} />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>FSI Calculations:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="fsiCalculations"
            control={control}
            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={fsiCalculationsSupported}
                    onChange={(event) => {
                      setFsiCalculationsSupported(event.target.checked);
                      handleOnChange(event);
                    }}
                    name="fsiCalculations"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>Data And Documentation:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="dataAndDocumentation"
            control={control}
            //defaultValue={formData.dataAndDocumentation.isSupported}

            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={dataAndDocumentationSupported}
                    onChange={(event) => {
                      setDataAndDocumentationSupported(event.target.checked);
                      handleOnChange(event);
                    }}
                    name="dataAndDocumentation"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>Site Analysis Report:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="siteAnalysisReport"
            control={control}
            //defaultValue={formData.siteAnalysisReport.isSupported}

            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={siteAnalysisReportSupported}
                    onChange={(event) => {
                      setSiteAnalysisReportSupported(event.target.checked);
                      handleOnChange(event);
                    }}
                    name="siteAnalysisReport"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>Market Analysis:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="marketAnalysis"
            control={control}
            //defaultValue={formData.marketAnalysis.isSupported}

            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={marketAnalysisSupported}
                    onChange={(event) => {
                      setMarketAnalysisSupported(event.target.checked);
                      handleOnChange(event);
                    }}
                    name="marketAnalysis"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>Feasibility Report:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="feasibilityReport"
            control={control}
            //defaultValue={formData.feasibilityReport.isSupported}

            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={feasibilityReportSupported}
                    onChange={(event) => {
                      setFeasibilityReportSupported(event.target.checked);
                      handleOnChange(event);
                    }}
                    name="feasibilityReport"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>financialCOPMOF:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="financialCOPMOF"
            control={control}
            //defaultValue={formData.financialCOPMOF.isSupported}

            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={financialCOPMOFSupported}
                    onChange={(event) => {
                      setFinancialCOPMOFSupported(event.target.checked);
                      handleOnChange(event);
                    }}
                    name="financialCOPMOF"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>cash Flows:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="cashFlows"
            control={control}
            //defaultValue={formData.cashFlows.isSupported}

            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={cashFlowsSupported}
                    onChange={(event) => {
                      setCashFlowsSupported(event.target.checked);
                      handleOnChange(event);
                    }}
                    name="cashFlows"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>Technical Feasibility:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="technicalFeasibility"
            control={control}
            //defaultValue={formData.technicalFeasibility.isSupported}

            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={technicalFeasibilitySupported}
                    onChange={(event) => {
                      setTechnicalFeasibilitySupported(event.target.checked);
                      handleOnChange(event);
                    }}
                    name="technicalFeasibility"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>Deemed Conveyance:</Typography>
          </FormControl>
        </Grid>

        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="deemedConveyance"
            control={control}
            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={deemedConveyanceSupported}
                    onChange={(event) => {
                      setDeemedConveyanceSupported(event.target.checked);
                      handleOnChange(event);
                    }}
                    name="deemedConveyance"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>Area Correction:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="areaCorrection"
            control={control}
            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={areaCorrectionSupported}
                    onChange={(event) => {
                      setAreaCorrectionSupported(event.target.checked);
                      handleOnChange(event);
                    }}
                    name="areaCorrection"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>Plot Subdivision:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="plotSubdivision"
            control={control}
            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={plotSubdivisionSupported}
                    onChange={(event) => {
                      setPlotSubdivisionSupported(event.target.checked);
                      handleOnChange(event);
                    }}
                    name="plotSubdivision"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>CTS Office Followup:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="ctsOffice"
            control={control}
            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={cTSofficeFollowupSupported}
                    onChange={(event) => {
                      setCTSofficeFollowupSupported(event.target.checked);
                      handleOnChange(event);
                    }}
                    name="ctsOffice"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>BMC File Approvals:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="bmcFileApprovals"
            control={control}
            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={bMCfileApprovalsSupported}
                    onChange={(event) => {
                      setBMCfileApprovalsSupported(event.target.checked);
                      handleOnChange(event);
                    }}
                    name="bmcFileApprovals"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>DP Remark 2034:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="dpRemark2034"
            control={control}
            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={dPRemark2034Supported}
                    onChange={(event) => {
                      setDPRemark2034Supported(event.target.checked);
                      handleOnChange(event);
                    }}
                    name="dpRemark2034"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>Search Report 30 years:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="searchReport30years"
            control={control}
            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={searchReport30yearsSupported}
                    onChange={(event) => {
                      setSearchReport30yearsSupported(event.target.checked);
                      handleOnChange(event);
                    }}
                    name="searchReport30years"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>Conveyance Deed:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="conveyanceDeed"
            control={control}
            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={conveyanceDeedSupported}
                    onChange={(event) => {
                      setConveyanceDeedSupported(event.target.checked);
                      handleOnChange(event);
                    }}
                    name="conveyanceDeed"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>Plot Physical Survey:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="plotPhysicalSurvey"
            control={control}
            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={plotPhysicalSurveySupported}
                    onChange={(event) => {
                      setPlotPhysicalSurveySupported(event.target.checked);
                      handleOnChange(event);
                    }}
                    name="plotPhysicalSurvey"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>Superimpose Plot Survey CTS plan:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="superImposePlotSurvey"
            control={control}
            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={superimposePlotSurveyCTSplanSupported}
                    onChange={(event) => {
                      setSuperimposePlotSurveyCTSplanSupported(
                        event.target.checked
                      );
                      handleOnChange(event);
                    }}
                    name="superImposePlotSurvey"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>Individual Flat Measurement:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="individualFlatMeasurement"
            control={control}
            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={individualFlatMeasurementSupported}
                    onChange={(event) => {
                      setIndividualFlatMeasurementSupported(
                        event.target.checked
                      );
                      handleOnChange(event);
                    }}
                    name="individualFlatMeasurement"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>79(a) Process:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="a79Process"
            control={control}
            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={a79ProcessSupported}
                    onChange={(event) => {
                      setA79ProcessSupported(event.target.checked);
                      handleOnChange(event);
                    }}
                    name="a79Process"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>All:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <FormControlLabel
            control={
              <Switch
                checked={selectAll}
                onChange={handleSelectAll}
                name="selectAll"
              />
            }
          />
        </Grid>

        <Grid item xs={12} sx={{ mt: 2 }}>
          <center>
            <Button
              size="medium"
              sx={{ mr: 3 }}
              onClick={() => onCancel()}
              variant="outlined"
              color="primary"
            >
              Cancel
            </Button>
            <Button
              size="medium"
              type="button"
              onClick={onSubmit}
              variant="contained"
              disabled={!isCheckboxChanged}
            >
              Save
            </Button>
          </center>
        </Grid>
      </Grid>
    </>
  );
};

export default PretenderingTab;
