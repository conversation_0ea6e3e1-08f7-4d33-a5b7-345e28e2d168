import React, { useContext, useEffect, useState } from "react";
import { Box, Typography, Card, Grid } from "@mui/material";
import {
  MdAssignment,
  MdLocationOn,
  MdDescription,
  MdOutlineAssignmentTurnedIn,
  MdReceipt,
} from "react-icons/md";
import { MdManageAccounts } from "react-icons/md";
import axios from "axios";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";
import { useRouter } from "next/router";
const StatisticsDashboard = () => {
  const [sampleData, setSampleData] = useState([]);
  const { user } = useContext(AuthContext);
  const router = useRouter();

  useEffect(() => {
    let url;
    if (user?.roleId === authConfig?.superAdminRoleId) {
      url =
        getUrl(authConfig.statisticsEndpointGraphs) +
        "/admin/service-requisitions-flow-count";
    } else {
      url =
        getUrl(authConfig.statisticsEndpointGraphs) +
        "/service-requisitions-flow-count";
    }

    let headers;

    if (user?.roleId === authConfig?.superAdminRoleId) {
      headers = getAuthorizationHeaders({
        accept:
          authConfig?.STATISTICS_GET_SERVICE_REQUISITIONS_FLOW_COUNT_ADMIN,
      });
    } else {
      headers = getAuthorizationHeaders({
        accept: authConfig?.STATISTICS_GET_SERVICE_REQUISITIONS_FLOW_COUNT,
      });
    }

    axios({
      method: "get",
      url: url,
      headers: headers,
    })
      .then((res) => {
        setSampleData(res?.data);
      })
      .catch((err) => console.log("Employees error", err));
  }, []);

  const statsData = [
    {
      label: "Total Service Requisitions",
      value: sampleData?.totalServiceRequisitionCount || 0,
      icon: <MdAssignment size={30} color="#108A00" />,
      page:"service-requisitions"
    },
    {
      label: "Booked Site Visits",
      value: sampleData?.totalSiteVisitsBookedCount || 0,
      icon: <MdLocationOn size={30} color="#49B549" />,
      page:"site-visits",
    },
    {
      label: "Quotations Submitted",
      value: sampleData?.quotationsSubmittedCount || 0,
      icon: <MdDescription size={30} color="#0B6300" />,
      page:"quotations"
    },
    {
      label: "Signed Work Orders",
      value: sampleData?.signedWorkOrderCount || 0,
      icon: <MdOutlineAssignmentTurnedIn size={30} color="#8DDF8D" />,
      page:"work-orders"
    },
    {
      label: "Invoices generated",
      value: sampleData?.totalInvoiceGeneratedCount || 0,
      icon: <MdReceipt size={30} color="#074300" />,
      page:"invoices"
    },
  ];

   
    const handleCardClick = (page) => {
      let queryParam;
    
      switch (page) {
        case "site-visits":
          queryParam = { bookedSiteVisits: true };
          break;
        case "work-orders":
            queryParam = { signedWorkOrders: true };
            break;
        case "quotations":
          queryParam = { quotationsSubmitted: true };
          break;
        case "invoices":
            queryParam = { overdueInvoices: true };
            break;
        default:
          queryParam = {};
      }
    
      router.push({
        pathname: `/${page}`,
        query: queryParam,
      });
    
  };

  return (
    <Card
      sx={{
        p: 4,
        borderRadius: "16px",
        boxShadow: "0px 4px 12px rgba(0, 0, 0, 0.1)",
      }}
    >
      {/* Dashboard Header */}
      <Typography
        variant="h6"
        sx={{
          fontWeight: "bold",
          mb: 4,
          display: "flex",
          alignItems: "center",
          gap: 1,
        }}
      >
        <MdManageAccounts size={24} color="#108A00" />
        SR Management Dashboard
      </Typography>

      {/* Horizontal Cards */}
      <Grid container spacing={3}>
        {statsData?.map((stat, index) => (
          <Grid item xs={12} key={index}>
            <Card
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                p: 3,
                borderRadius: "12px",
                boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.1)",
                transition: "transform 0.3s ease",
                "&:hover": {
                  transform: "scale(1.02)",
                },
                cursor: "pointer",
              }}
              onClick={() => handleCardClick(stat.page)}
            >
              {/* Icon Section */}
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  width: 50,
                  height: 50,
                  borderRadius: "50%",
                  backgroundColor: "#f1f1f1",
                }}
              >
                {stat.icon}
              </Box>

              {/* Text Section */}
              <Box sx={{ flex: 1, ml: 3 }}>
                <Typography
                  variant="body1"
                  sx={{
                    fontWeight: "bold",
                    color: "#555",
                  }}
                >
                  {stat.label}
                </Typography>
              </Box>

              {/* Value Section */}
              <Typography
                variant="h5"
                sx={{
                  fontWeight: "bold",
                  color: "#333",
                }}
              >
                {stat.value}
              </Typography>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Card>
  );
};

export default StatisticsDashboard;
