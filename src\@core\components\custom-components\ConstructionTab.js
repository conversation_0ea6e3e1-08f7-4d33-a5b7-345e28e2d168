import { useState, useContext, useEffect } from "react";
import { Divider } from "@mui/material";

// ** MUI Imports
import Grid from "@mui/material/Grid";
import Button from "@mui/material/Button";
import FormControl from "@mui/material/FormControl";
import { AuthContext } from "src/context/AuthContext";

// ** Third Party Imports
import * as yup from "yup";
import { useForm, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { FormControlLabel, Switch, Typography } from "@mui/material";

const defaultValues = {
  email: "",
  lastName: "",
  password: "",
  firstName: "",
};

const showErrors = (field, valueLen, min) => {
  if (valueLen === 0) {
    return `${field} field is required`;
  } else if (valueLen > 0 && valueLen < min) {
    return `${field} must be at least ${min} characters`;
  } else {
    return "";
  }
};

const schema = yup.object().shape({
  email: yup.string().email().required(),
  lastName: yup
    .string()
    .min(3, (obj) => showErrors("lastName", obj.value.length, obj.min))
    .required(),
  password: yup
    .string()
    .min(8, (obj) => showErrors("password", obj.value.length, obj.min))
    .required(),
  firstName: yup
    .string()
    .min(3, (obj) => showErrors("firstName", obj.value.length, obj.min))
    .required(),
});

const ConstructionTab = ({
  onCancel,
  setter,
  defaultData,
  setUnsavedChanges,
}) => {
  const { updateEntityServices } = useContext(AuthContext);

  // ** States
  const [state, setState] = useState({
    password: "",
    showPassword: false,
  });

  // ** Hook
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues,
    mode: "onChange",
    resolver: yupResolver(schema),
  });

  const [formData, setFormData] = useState(defaultData);
  const [
    societyCoordinationThroughoutProjectSupported,
    setSocietyCoordinationThroughoutProjectSupported,
  ] = useState(defaultData.societyCoordinationThroughoutProject.isSupported);

  const [materialManagementSupported, setMaterialManagementSupported] =
    useState(defaultData.materialManagement.isSupported);

  const [periodicalTestingSupported, setPeriodicalTestingSupported] = useState(
    defaultData.periodicalTesting.isSupported
  );

  const [
    siteSupervisionAndQualityControlSupported,
    setSiteSupervisionAndQualityControlSupported,
  ] = useState(defaultData.siteSupervisionAndQualityControl.isSupported);

  const [timeLineMonitoringSupported, setTimeLineMonitoringSupported] =
    useState(defaultData.timeLineMonitoring.isSupported);

  const [selectAll, setSelectAll] = useState(false);

  const handleSelectAll = (event) => {
    const { checked } = event.target;

    setSocietyCoordinationThroughoutProjectSupported(checked);
    setMaterialManagementSupported(checked);
    setPeriodicalTestingSupported(checked);
    setSiteSupervisionAndQualityControlSupported(checked);
    setTimeLineMonitoringSupported(checked);

    setSelectAll(checked);
    setFormData((prev) => ({
      ...prev,
      societyCoordinationThroughoutProject: {
        ...prev.societyCoordinationThroughoutProject,
        isSupported: checked,
      },
      materialManagement: { ...prev.materialManagement, isSupported: checked },
      periodicalTesting: { ...prev.periodicalTesting, isSupported: checked },
      siteSupervisionAndQualityControl: {
        ...prev.siteSupervisionAndQualityControl,
        isSupported: checked,
      },
      timeLineMonitoring: { ...prev.timeLineMonitoring, isSupported: checked },
    }));

    setUnsavedChanges(true);
    setIsCheckboxChanged(true);
  };

  useEffect(() => {
    if (
      societyCoordinationThroughoutProjectSupported &&
      materialManagementSupported &&
      periodicalTestingSupported &&
      siteSupervisionAndQualityControlSupported &&
      timeLineMonitoringSupported
    ) {
      setSelectAll(true);
    } else {
      setSelectAll(false);
    }
  }, [
    societyCoordinationThroughoutProjectSupported,
    materialManagementSupported,
    periodicalTestingSupported,
    siteSupervisionAndQualityControlSupported,
    timeLineMonitoringSupported,
  ]);
  const [isCheckboxChanged, setIsCheckboxChanged] = useState(false);

  const handleCheckboxChange = (event) => {
    setIsCheckboxChanged(true);
    handleOnChange(event);
  };

  const handleOnChange = (event) => {
    const { name, checked } = event.target;

    switch (name) {
      case "societyCoordinationThroughoutProject":
        setFormData({
          ...formData,
          societyCoordinationThroughoutProject: {
            ...formData.societyCoordinationThroughoutProject,
            isSupported: checked,
          },
        });
        break;
      case "materialManagement":
        setFormData({
          ...formData,
          materialManagement: {
            ...formData.materialManagement,
            isSupported: checked,
          },
        });
        break;
      case "periodicalTesting":
        setFormData({
          ...formData,
          periodicalTesting: {
            ...formData.periodicalTesting,
            isSupported: checked,
          },
        });
        break;
      case "siteSupervisionAndQualityControl":
        setFormData({
          ...formData,
          siteSupervisionAndQualityControl: {
            ...formData.siteSupervisionAndQualityControl,
            isSupported: checked,
          },
        });
        break;
      case "timeLineMonitoring":
        setFormData({
          ...formData,
          timeLineMonitoring: {
            ...formData.timeLineMonitoring,
            isSupported: checked,
          },
        });
        break;
    }
    setUnsavedChanges(true);
    setIsCheckboxChanged(true);
  };

  async function onSubmit() {
    setUnsavedChanges(false);
    const response = await updateEntityServices(
      { constructionStage: formData },
      () => {
        console.error("Construction Stage Details failed");
      }
    );

    if (response) {
      setFormData(response);
      setter(formData);
    }

    console.log("Submitted data from Section3:", response);
    onCancel();
  }

  return (
    <>
      <Grid container sx={{ display: "flex", alignItems: "center" }}>
        <Grid item xs={8} sm={4}>
          <FormControl fullWidth>
            <Typography sx={{ fontWeight: 600 }}>Service Name:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={9} sm={2}>
          <Typography sx={{ fontWeight: 600 }}>(Yes / No):</Typography>
        </Grid>

        <Grid item xs={12}>
          <Divider sx={{ my: (theme) => `${theme.spacing(2)} !important` }} />
        </Grid>
        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>Society Coordination Throughout Project:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="societyCoordinationThroughoutProject"
            control={control}
            //defaultValue={formData.societyCoordinationThroughoutProject.isSupported}

            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={societyCoordinationThroughoutProjectSupported}
                    onChange={(event) => {
                      setSocietyCoordinationThroughoutProjectSupported(
                        event.target.checked
                      );
                      handleOnChange(event);
                    }}
                    name="societyCoordinationThroughoutProject"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>Material Management:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="materialManagement"
            control={control}
            //defaultValue={formData.materialManagement.isSupported}

            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={materialManagementSupported}
                    onChange={(event) => {
                      setMaterialManagementSupported(event.target.checked);
                      handleOnChange(event);
                    }}
                    name="materialManagement"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>Periodical Testing:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="periodicalTesting"
            control={control}
            //defaultValue={formData.periodicalTesting.isSupported}

            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={periodicalTestingSupported}
                    onChange={(event) => {
                      setPeriodicalTestingSupported(event.target.checked);
                      handleOnChange(event);
                    }}
                    name="periodicalTesting"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>Site Supervision and Quality Control:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="siteSupervisionAndQualityControl"
            control={control}
            //defaultValue={formData.siteSupervisionAndQualityControl.isSupported}

            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={siteSupervisionAndQualityControlSupported}
                    onChange={(event) => {
                      setSiteSupervisionAndQualityControlSupported(
                        event.target.checked
                      );
                      handleOnChange(event);
                    }}
                    name="siteSupervisionAndQualityControl"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>TimeLine Monitoring:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="timeLineMonitoring"
            control={control}
            //defaultValue={formData.timeLineMonitoring.isSupported}

            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={timeLineMonitoringSupported}
                    onChange={(event) => {
                      setTimeLineMonitoringSupported(event.target.checked);
                      handleOnChange(event);
                    }}
                    name="timeLineMonitoring"
                  />
                }
              />
            )}
          />
        </Grid>
        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>All:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <FormControlLabel
            control={
              <Switch
                checked={selectAll}
                onChange={handleSelectAll}
                name="selectAll"
              />
            }
          />
        </Grid>

        <Grid item xs={12} sx={{ mt: 2 }}>
          <center>
            <Button
              size="medium"
              sx={{ mr: 3 }}
              onClick={() => onCancel()}
              variant="outlined"
              color="primary"
            >
              Cancel
            </Button>
            <Button
              size="medium"
              type="button"
              onClick={onSubmit}
              variant="contained"
              disabled={!isCheckboxChanged}
            >
              Save
            </Button>
          </center>
        </Grid>
      </Grid>
    </>
  );
};

export default ConstructionTab;
