import Box from '@mui/material/Box'
import MenuItem from '@mui/material/MenuItem'
import InputLabel from '@mui/material/InputLabel'
import FormControl from '@mui/material/FormControl'
import Select from '@mui/material/Select'
import { Controller } from 'react-hook-form'

const options = [
  { value: 'ARCHITECT', key: 'Architect' },
  { value: 'BROKER', key: 'Broker' },
  { value: 'CHARTERED_ACCOUNTANT', key: 'Chartered Accountant' },
  { value: 'SOCIETY', key: 'Society' },
  { value: 'PMC', key: 'PMC' },
  { value: 'STRUCTURAL_ENGINEER', key: 'Structural Engineer' },
]

const SelectUser = (props) => {
  // ** Props
  const { id, label, onChange, selectedOption } = props
  return (
    <>
      <Box sx={{ '& > *': { mt: 1, mr: 6 } }}>
        <FormControl sx={{ maxWidth: '100%', minWidth: '100%' }}>
          <InputLabel id={id + '-label'}>{label}</InputLabel>
          <Select
            label={label}
            value={selectedOption}
            onChange={onChange}
            id={id}
            labelId={id + '-label'}
          >
            {options.map((option) => (
              <MenuItem key={option.value} value={option.value}>
                {option.key}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Box>
    </>
  )
}

export default SelectUser
