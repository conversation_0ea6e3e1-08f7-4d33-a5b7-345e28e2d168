import React, { useState } from 'react';
import Box from "@mui/material/Box";
import CustomAutocomplete from './CustomAutoComplete';
import CustomTextField from './CustomTextField';

const SelectClearAutoComplete = (props) => {
  const [open, setOpen] = useState(false);
  const {
    id,
    label,
    nameArray,
    value,
    onChange,
  } = props;

  const handleOptionSelect = (event, option) => {
    event.stopPropagation();
    const selectedValue = option ? option.value : null;
    onChange({ target: { value: selectedValue } });
    setOpen(false);  // Close the dropdown
  };

  const handleOpenToggle = () => {
    setOpen(!open);
  };

  return (
    <Box sx={{ position: 'relative', zIndex: 1 }}>
      <CustomAutocomplete
        autoHighlight
        id={id}
        options={nameArray}
        getOptionLabel={(option) => option.key || ''}
        value={nameArray?.find((option) => option.value === value) || null}
        onChange={handleOptionSelect}
        onOpen={handleOpenToggle}
        onClose={handleOpenToggle}
        open={open}
        renderOption={(props, option) => (
          <Box
            component='li'
            {...props}
            onClick={(event) => handleOptionSelect(event, option)}
          >
            {option.key}
          </Box>
        )}
        renderInput={(params) => (
          <CustomTextField
            {...params}
            size='small'
            label={label}
            placeholder={`${label}`}
            InputLabelProps={{
              ...params.InputLabelProps,
              shrink: true,
              sx: { fontWeight: 'bold' }
            }}
            sx={{
              "& label": {
                fontWeight: 'bold',
              }
            }}
          />
        )}
      />
    </Box>
  );
};

export default SelectClearAutoComplete;
