import React, { useContext, useEffect, useState } from "react";
import { Box, Typography, Card } from "@mui/material";
import {
  MdAssignment,
  MdLocationOn,
  MdDescription,
  MdWork,
  MdReceipt,
} from "react-icons/md";
import { AiOutlineBarChart } from "react-icons/ai";
import { useRouter } from "next/router"; // Import useRouter for navigation
import axios from "axios";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";

const StatisticsCards = () => {
  const [sampleData, setSampleData] = useState([]);
  const router = useRouter(); // Initialize useRouter

  const { user } = useContext(AuthContext)

  useEffect(() => {
    let url;
    if (user?.roleId === authConfig?.superAdminRoleId) {
      url = getUrl(
        authConfig?.statisticsEndpointGraphs +
          "/service-requisitions-flow-weekly-count"
      );
    } else {
      url = getUrl(
        authConfig?.statisticsEndpointGraphs +
          "/employee/service-requisitions-flow-weekly-count"
      );
    }

    axios({
      method: "get",
      url: url,
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setSampleData(res?.data);
      })
      .catch((err) => console.log("Employees error", err));
  }, []);

  const iconMapping = {
    "Service Requisitions": <MdAssignment size={32} color="#108A00" />,
    "Site Visits": <MdLocationOn size={32} color="#49B549" />,
    "Quotations": <MdDescription size={32} color="#0B6300" />,
    "Signed Work Orders": <MdWork size={32} color="#074300" />,
    "Invoices": <MdReceipt size={32} color="#8DDF8D" />,
  };

  const routeMapping = {
    "Service Requisitions": "/service-requisitions",
    "Site Visits": "/site-visits",
    "Quotations": "/quotations",
    "Signed Work Orders": "/work-orders",
    "Invoices": "/invoices",
  };

  const queryMapping = {
    "Service Requisitions": "currentWeekSRs",
    "Site Visits": "currentWeekSiteVisits",
    "Quotations": "currentWeekQuotations",
    "Signed Work Orders": "currentWeekWorkOrders",
    "Invoices": "currentWeekInvoices",
  };

  const handleCardClick = (label) => {
    const route = routeMapping[label];
    const queryParam = queryMapping[label];
    if (route && queryParam) {
      router.push({
        pathname: route,
        query: { [queryParam]: true },
      });
    }
  };

  const statsData = sampleData?.map((item) => ({
    label: item.metricName,
    value: item.currentCount,
    growth: `${item.percentageChange > 0 ? "+" : ""}${
      item.percentageChange
    }% than last week`,
    icon: iconMapping[item.metricName] || (
      <MdAssignment size={32} color="#000" />
    ),
  }));

  return (
    <Card sx={{ p: 2.5, borderRadius: "16px" }}>
      <Typography
        variant="h6"
        sx={{
          fontWeight: "bold",
          mb: 2,
          display: "flex",
          alignItems: "center",
          gap: 1,
        }}
      >
        <AiOutlineBarChart size={28} color="#108A00" />
        Weekly SR Statistics Overview
      </Typography>
      <Box>
        {statsData?.map((item, index) => (
          <Box
            key={index}
            sx={{
              display: "flex",
              alignItems: "center",
              mb: 3,
              border: "1px solid #ddd",
              borderRadius: "12px",
              p: 3,
              cursor: "pointer",
              backgroundColor: item.color,
              transition: "background-color 0.3s",
              "&:hover": {
                backgroundColor: "#f9f9f9",
              },
            }}
            onClick={() => handleCardClick(item.label)} 
          >
            <Box
              sx={{
                mr: 3,
                p: 2,
                borderRadius: "50%",
                backgroundColor: "#f0f0f0",
              }}
            >
              {item.icon}
            </Box>
            <Box sx={{ flex: 1 }}>
              <Typography variant="body1" sx={{ fontWeight: "bold", mb: 1 }}>
                {item.label}
              </Typography>
              <Typography
                variant="h5"
                sx={{ fontWeight: "bold", color: "#333", mb: 0 }}
              >
                {item.value}
              </Typography>
            </Box>
            <Typography
              variant="body2"
              sx={{
                fontWeight: "bold",
                color: item.growth.includes("-") ? "#e74c3c" : "#108A00",
                ml: 2,
              }}
            >
              {item.growth}
            </Typography>
          </Box>
        ))}
      </Box>
    </Card>
  );
};

export default StatisticsCards;
