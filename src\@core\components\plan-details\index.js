import { useContext, useEffect, useState } from "react";
// ** MUI Imports
import CancelIcon from "@mui/icons-material/Cancel";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import IconButton from "@mui/material/IconButton";
import { styled } from "@mui/material/styles";
import Icon from "src/@core/components/icon";
import { AuthContext } from "src/context/AuthContext";

import StarIcon from "@mui/icons-material/Star";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Divider,
  Grid,
  Typography,
} from "@mui/material";
import Link from "next/link";
import CheckBoxF from "../custom-components/CheckBoxF";

const BoxWrapper = styled(Box)(({ theme }) => ({
  padding: theme.spacing(6),
  borderRadius: theme.shape.borderRadius,
  width: "183px",
  display: "flex",
  flexDirection: "column",
  justifyContent: "space-between",
  height: "auto",
  textAlign: "center",
  flexGrow: 1,
  [theme.breakpoints.down(821)]: {
    width: "200px",
    margin: theme.spacing(2, 0),
  },
}));

const BoxFeature = styled(Box)(({ theme }) => ({
  marginBottom: theme.spacing(4),
  "& > :not(:last-child)": {
    marginBottom: theme.spacing(2.5),
  },
}));
const CustomLink = styled(Link)({
  textDecoration: "none",
});

const FixedButton = styled(Button)(({ theme }) => ({
  height: "40px",
  width: "145px",
  whiteSpace: "nowrap",
}));

const PlanDetails = (props) => {
  const { data } = props;

  //
  const [openDialog, setOpenDialog] = useState(false);
  const [isChecked, setIsChecked] = useState(false);
  const [showRazorPay, setShowRazorPay] = useState(false);
  const [openPaymentDialog, setOpenPaymentDialog] = useState(false);
  const [dialogMessage, setDialogMessage] = useState("");
  const [paymentStatus, setPaymentStatus] = useState(null); // null, "success", or "failure"
  const [paymentErrorDescription, setPaymentErrorDescription] = useState(""); // Add state for error description
  const [subscriptionData, setSubscriptionResponse] = useState("");
  const {
    user,
    createSubscriptionPost,
    createPaymentPost,
    updateSubscription,
    subscriptionResponseData,
    fetchRazorPayId,
    userPackageData,
    setUserPackage,
  } = useContext(AuthContext);

  useEffect(() => {
    const loadRazorpayScript = () => {
      const script = document.createElement("script");
      script.src = "https://checkout.razorpay.com/v1/checkout.js";
      script.async = true;
      script.onload = () => console.log("Razorpay script loaded successfully");
      document.body.appendChild(script);
    };
    loadRazorpayScript();

    return () => {
      const razorpayScript = document.querySelector(
        'script[src="https://checkout.razorpay.com/v1/checkout.js"]'
      );
      if (razorpayScript) {
        document.body.removeChild(razorpayScript);
      }
    };
  }, []);

  const handleTermsAndConditions = () => {
    setOpenDialog(true);
  };

  const handleDialogClose = () => {
    setIsChecked(false);
    setOpenDialog(false);
    setOpenPaymentDialog(false);
  };

  const handleCheckboxChange = () => {
    setIsChecked(!isChecked);
  };
const [isDisable, setIsDisable] = useState(false);
  const proceedPayment = async () => {
    setIsDisable(true);
    if (!window.Razorpay) {
      alert("Razorpay SDK failed to load. Are you online?");
      return;
    }

    const subscriptionPostPayload = {
      packageId: data?.id,
      price: data?.price * 100,
    };

    try {
      // Await the result of the subscription post call
      const subscriptionPostResponse = await createSubscriptionPost(
        subscriptionPostPayload
      );
      setSubscriptionResponse(subscriptionPostResponse);
      // Ensure you use the subscriptionPostResponse to get orderId and subscriptionId
      setOpenDialog(false);
      const subscriptionResponseData = subscriptionPostResponse;
      const currency = "INR";
      const paymentGateway = "razorpay";
      const amountInPaisa = data?.price * 100;

      const payloadToRazorpay = {
        key: "rzp_test_WydCsRrfzPdpt3",
        currency: currency,
        amount: amountInPaisa,
        name: data?.name,
        description: `${data?.name} Plan Transaction`,
        image: "/images/logo.webp",
        order_id: subscriptionResponseData?.orderId || "",
        handler: async function (response) {
          if (response?.razorpay_payment_id) {
            try {
              const paymentById = await fetchRazorPayId(
                response?.razorpay_payment_id
              );
              const paymentMethod = paymentById?.method;
              const errorDescription = paymentById?.errorDescription;
              const paymentPostPayload = {
                subscriptionId: subscriptionResponseData?.subscriptionId,
                price: amountInPaisa,
                currency: currency,
                paymentGateway: paymentGateway,
                paymentMethod: paymentMethod,
                failureDescription: errorDescription,
                orderId: response?.razorpay_order_id,
                razorpayPaymentId: response?.razorpay_payment_id,
                razorpaySignature: response?.razorpay_signature,
              };

              const paymentPostResponse = await createPaymentPost(
                paymentPostPayload
              );

              if (paymentPostResponse) {
                setUserPackage(paymentPostResponse);

                setPaymentStatus("success"); // Set payment status to success
                setOpenPaymentDialog(true); // Open success dialog
                const message = `
                <div> 
                  <h3>Your payment has been processed successfully.</h3>
                </div>
              `;
                setDialogMessage(message);
              }
            } catch (error) {
              console.error("Error fetching payment details:", error);
            }
          }
        },
        prefill: {
          name: data?.name || "",
        },
        notes: {
          address: "Razorpay Corporate Office",
        },
        theme: {
          color: "#3399cc",
        },
        modal: {
          ondismiss: function () {},
          width: "80% !important",
          height: "100% !important",
        },
      };

      var rzp1 = new window.Razorpay(payloadToRazorpay);

      // Payment failed event handler
      rzp1.on("payment.failed", async function (response) {
        console.log("PAYMENT FAILED RESPONSE", response.error?.description);

        try {
          const subscriptionPatchPayload = {
            id: subscriptionResponseData?.subscriptionId,
            error: response.error?.description || "Unknown error",
          };

          await updateSubscription(subscriptionPatchPayload);
        } catch (error) {
          console.error("Error failure status", error);
        }

        setPaymentErrorDescription(
          response.error?.description || "Payment failed"
        ); // Store the error description

        setPaymentStatus("failure"); // Set payment status to failure
        setOpenPaymentDialog(true); // Open failure dialog
        setDialogMessage(response.error?.description || "Payment failed");
      });

      rzp1.open();
    } catch (error) {
      console.error("Error creating subscription:", error);
    } finally {
      setIsDisable(false);
    }
  };

  const renderFeaturesAndExclusions = () => {
    const benefits = [];
    const exclusions = [];

    if (data?.listing) benefits.push("Listing");
    if (data?.microSite) benefits.push("Microsite");
    if (data?.audioVisualIncluded)
      benefits.push(`Audio Visual (${data.audioVisualDurationInMinutes} mins)`);
    if (data?.editorialIncluded)
      benefits.push(`Editorial (${data.editorialDurationInMinutes} mins)`);
    if (data?.podcastIncluded) {
      benefits.push(
        `Podcast (${
          data.podcastDurationInMinutes
            ? data.podcastDurationInMinutes + " mins"
            : ""
        })`
      );
    } else {
      exclusions.push("Podcast");
    }
    if (!data?.editorialIncluded) exclusions.push("Editorial");
    if (!data?.audioVisualIncluded) exclusions.push("Audio Visual");

    return (
      <Box>
        {benefits.map((item, index) => (
          <Box
            key={`benefit-${index}`}
            sx={{ display: "flex", alignItems: "start" }}
          >
            <IconButton
              sx={{
                padding: "0px",
                color: "primary.main",
                fontSize: "1rem !important",
                transform: "scale(0.7)",
              }}
            >
              <CheckCircleIcon />
            </IconButton>
            <Typography sx={{ color: "text.secondary", ml: 1 }}>
              {item}
            </Typography>
          </Box>
        ))}
        {exclusions.map((item, index) => (
          <Box
            key={`exclusion-${index}`}
            sx={{ display: "flex", alignItems: "start" }}
          >
            <IconButton
              sx={{
                padding: "0px",
                color: "error.main",
                fontSize: "1rem !important",
                transform: "scale(0.7)",
              }}
            >
              <CancelIcon />
            </IconButton>
            <Typography sx={{ color: "text.secondary", ml: 1 }}>
              {item}
            </Typography>
          </Box>
        ))}
      </Box>
    );
  };

  return (
    <>
      <Box sx={{ padding: 4 }}>
        <Grid container spacing={12} justifyContent="space-between">
          <Grid item xs={12} sm={2} md={4}>
            <BoxWrapper
              sx={{
                position: "relative",
              }}
            >
              {userPackageData === data?.id && (
                <StarIcon
                  color="primary"
                  sx={{
                    position: "absolute",
                    top: 0,
                    right: 0,
                    fontSize: "2rem",
                  }}
                />
              )}
              <Typography
                sx={{ fontWeight: 500, mb: 3, fontSize: "1.8125rem" }}
              >
                {data?.name}
              </Typography>
              <Typography
                color="primary"
                sx={{ fontSize: "1.9rem", fontWeight: 500 }}
              >
                ₹ {data?.price}{" "}
                <Typography component="span" variant="body2">
                  /Yr
                </Typography>
              </Typography>
              {!userPackageData ? (
                user?.organisationCategory === "EMPLOYEE" ?(
                  null
                ):(
                  <Button
                  variant="contained"
                  onClick={handleTermsAndConditions}
                  sx={{ mt: 5, mb: 5, fontSize: "13.5px" }}
                  size="medium"
                >
                  {"Choose Plan"}
                </Button>
                )
                
              ) : (
                userPackageData === data?.id && (
                  <Typography
                    sx={{
                      mt: 2,
                      mb: 2,
                      fontSize: "18.5px",
                      color: "primary.main",
                      fontWeight: 500,
                    }}
                  >
                    Current Plan
                  </Typography>
                )
              )}

              <Box>{renderFeaturesAndExclusions()}</Box>
            </BoxWrapper>
          </Grid>
        </Grid>
      </Box>

      {/* Dialog for Payment Terms and Conditions */}
      <Dialog
        fullWidth
        maxWidth="sm"
        scroll="paper"
        open={openDialog}
        onClose={(event, reason) => {
          if (reason !== "backdropClick") {
            handleDialogClose();
          }
        }}
      >
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.5)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
          textAlign={"center"}
          fontSize={"18px !important"}
          fontWeight={"bold"}
        >
          {"Checkout"}
          <Box sx={{ position: "absolute", top: "4px", right: "14px" }}>
            <IconButton
              size="small"
              onClick={handleDialogClose}
              sx={{
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent
          sx={{
            position: "relative",
            p: (theme) => `${theme.spacing(6, 8)} !important`,
          }}
        >
          <Box mb={1} sx={{ display: "flex", justifyContent: "space-between" }}>
            <Typography fontWeight={500} sx={{ fontSize: "1rem" }}>
              {data?.name}
            </Typography>
            <Typography fontWeight={500} sx={{ fontSize: "1rem" }}>
              ₹{data?.price}
            </Typography>
          </Box>
          <Divider />
          <Box
            mt={1}
            sx={{ display: "flex", justifyContent: "end", gap: "1rem" }}
          >
            <Typography fontWeight={500} sx={{ fontSize: "1rem" }}>
              Total :
            </Typography>
            <Typography fontWeight={500} sx={{ fontSize: "1rem" }}>
              ₹{data?.price}
            </Typography>
          </Box>
          <Divider sx={{ marginTop: "6px" }} />
          <Typography sx={{ fontSize: "0.9rem !important", marginTop: "3rem" }}>
            Before proceeding to payment, please ensure you have read,
            understood, and signed our{" "}
            <CustomLink
              sx={{ textDecoration: "none !important" }}
              href="/pdf/Professional_Agreement.pdf"
              target="_blank"
            >
              Terms and Conditions and Privacy Policy
            </CustomLink>
            .
          </Typography>
          <Box
            sx={{
              display: "flex",
              paddingTop: "0.6rem !important",
              alignItems: "start",
            }}
          >
            <CheckBoxF checked={isChecked} onChange={handleCheckboxChange} />
            <Typography sx={{ fontSize: "0.7rem !important" }}>
              By checking this box, you confirm that you have signed the
              required document and agree to its terms and conditions.
            </Typography>
          </Box>
          <Grid
            sx={{
              display: "flex",
              justifyContent: "center",
              marginTop: "1rem",
            }}
          >
            <Button
              disabled={!isChecked || isDisable}
              variant="contained"
              onClick={proceedPayment}
              color="primary"
              sx={{ textAlign: "center" }}
            >
              Proceed to Payment
            </Button>
          </Grid>
          {showRazorPay && <RazorPay data={data} />}
        </DialogContent>
      </Dialog>

      <Dialog
        open={openPaymentDialog}
        onClose={handleDialogClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleDialogClose}
              sx={{ margin: "auto", width: 100 }}
            >
              Okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

export default PlanDetails;
