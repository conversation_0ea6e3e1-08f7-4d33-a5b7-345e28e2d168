import React from "react";

export const fetchIpAddress = async () => {
  try {
    const response = await fetch("https://api.ipify.org?format=json");
    const data = await response.json();
    return data.ip;
  } catch (error) {
    console.error("Error fetching IP address:", error);
    return null;
  }
};

const FetchIpAddress = () => {
  return null; // This component is just a placeholder for export purposes
};

export default FetchIpAddress;
