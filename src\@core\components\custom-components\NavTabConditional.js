// ** React Imports
import { useState } from 'react'

// ** MUI Imports
import Tab from '@mui/material/Tab'
import TabList from '@mui/lab/TabList'
import TabPanel from '@mui/lab/TabPanel'
import TabContext from '@mui/lab/TabContext'
import Typography from '@mui/material/Typography'

const NavTabConditional = props => {

  // ** Props
  const { tabContent1, tabContent2, tabContent3, tabContent4,currentTab, handleTabChange } = props

  return (    
    
    <TabContext value={currentTab}>
      <TabList
        variant="scrollable"
        scrollButtons="auto"
        onChange={handleTabChange}
        aria-label="forced scroll tabs example"
      >
        <Tab
          value="1"
          component="a"
          label="Pre-Agreement"
          href="/drafts"
          onClick={(e) => e.preventDefault()}
        />
        <Tab
          value="2"
          component="a"
          label="Tendering Stage"
          href="/trash"
          onClick={(e) => e.preventDefault()}
        />
        <Tab
          value="3"
          component="a"
          label="Construction Stage"
          href="/spam"
          onClick={(e) => e.preventDefault()}
        />
        {tabContent4 && (
          <Tab
            value="4"
            component="a"
            label="Other Service"
            href="/spam"
            onClick={(e) => e.preventDefault()}
          />
        )}
      </TabList>
      <TabPanel value="1" sx={{ pb: 1, pt: 3, px: { xs: "0", md: "0.5rem" } }}>
        <Typography>{tabContent1}</Typography>
      </TabPanel>
      <TabPanel value="2" sx={{ pb: 1, pt: 3, px: { xs: "0", md: "0.5rem" } }}>
        <Typography>{tabContent2}</Typography>
      </TabPanel>
      <TabPanel value="3" sx={{ pb: 1, pt: 3, px: { xs: "0", md: "0.5rem" } }}>
        <Typography>{tabContent3}</Typography>
      </TabPanel>
      <TabPanel value="4" sx={{ pb: 1, pt: 3, px: { xs: "0", md: "0.5rem" } }}>
        <Typography>{tabContent4}</Typography>
      </TabPanel>
    </TabContext>
  );
}

export default NavTabConditional
