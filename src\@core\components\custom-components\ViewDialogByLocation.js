import React, { useContext, useEffect, useState } from "react";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import { Typography, IconButton } from "@mui/material";
import { 
  Close as CloseIcon,
  CropSquareOutlined,
  MinimizeSharp,
} from "@mui/icons-material";
import { AuthContext } from "src/context/AuthContext";

const ViewDialogByLocation = ({ location, setSelectedLocation }) => {
  const { getFileByLocation } = useContext(AuthContext);
  const [fileContent, setFileContent] = useState(null);
  const [imageSrc, setImageSrc] = useState(null);
  const [isLarge, setIsLarge] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await getFileByLocation(location);
        setFileContent(response?.data?.data);
      } catch (error) {
        console.error("Error fetching file content:", error);
      }
    };

    if (location) {
      fetchData();
    }
  }, [getFileByLocation, location]);

  useEffect(() => {
    if (fileContent) {
      const dataUriPrefix = "data:image/png;base64,";
      const base64String = fileContent.includes(dataUriPrefix)
        ? fileContent.split(dataUriPrefix)[1]
        : fileContent;

      const decodedString = Buffer.from(base64String, "base64").toString(
        "binary"
      );

      const byteCharacters = decodedString;
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: "image/*" });

      const imageUrl = URL.createObjectURL(blob);

      setImageSrc(imageUrl);

      return () => {
        URL.revokeObjectURL(imageUrl);
      };
    }
  }, [fileContent]);

  const onClose = () => {
    setImageSrc(null);
    setFileContent(null);
    setSelectedLocation(null);
  };

  const toggleSize = () => {
    setIsLarge((prevIsLarge) => !prevIsLarge);
  };

  return (
    <Dialog
      open={Boolean(location)}
      onClose={onClose}
      maxWidth={isLarge ? "xl" : "md"}
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Typography>
          {location && location?.split("/").pop()}
        </Typography>

        <div
          style={{
            position: "absolute",
            top: 0,
            right: 0,
            margin: "8px",
          }}
        >
          <IconButton
            edge="end"
            color="inherit"
            style={{ margin: "8px" }}
            aria-label="toggle-size"
            onClick={toggleSize}
          >
            {isLarge ? <MinimizeSharp /> : <CropSquareOutlined />}
          </IconButton>
          <IconButton
          id="closeViewDialog"
            edge="end"
            color="inherit"
            onClick={onClose}
            style={{ margin: "8px" }}
            aria-label="close"
          >
            <CloseIcon />
          </IconButton>
        </div>
      </DialogTitle>
      <DialogContent
        sx={{
          width: isLarge ? "800px" : "500px",
          height: isLarge ? "500px" : "300px",
          position: "relative",
        }}
      >
        {imageSrc && (
          <img
            src={imageSrc}
            alt={location}
            style={{
              position: "absolute",
              top: "50%",
              left: "50%",
              transform: "translate(-50%, -50%)",
              maxWidth: "90%",
              maxHeight: "90%",
            }}
          />
        )}
      </DialogContent>
    </Dialog>
  );
};

export default ViewDialogByLocation;
