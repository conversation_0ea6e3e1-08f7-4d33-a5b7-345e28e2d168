import dynamic from 'next/dynamic';
import React from 'react';

// Dynamically import 'react-apexcharts' to avoid SSR issues
const ApexChart = dynamic(() => import('react-apexcharts'), { ssr: false });

const StrokedGaugeCircleChart = () => {
  const state = {
    series: [67], // Percentage of Enrolled Service Providers
    options: {
      chart: {
        height: 350,
        type: 'radialBar',
        offsetY: -10,
      },
      plotOptions: {
        radialBar: {
          startAngle: -135,
          endAngle: 135,
          dataLabels: {
            name: {
              fontSize: '16px',
              offsetY: 120,
            },
            value: {
              offsetY: 76,
              fontSize: '22px',
              formatter: function (val) {
                return val + "%";
              },
            },
          },
        },
      },
      fill: {
        type: 'gradient',
        gradient: {
          shade: 'dark',
          shadeIntensity: 0.15,
          inverseColors: false,
          opacityFrom: 1,
          opacityTo: 1,
          stops: [0, 50, 65, 91],
        },
      },
      stroke: {
        dashArray: 4,
      },
      labels: ['Enrolled Service Providers'], // Updated label
    },
  };

  return (
    <div style={{ width: '100%', display: 'flex', justifyContent: 'center' }}>
      <div id="chart">
        <ApexChart options={state.options} series={state.series} type="radialBar" height={350} />
      </div>
    </div>
  );
};

export default StrokedGaugeCircleChart;
