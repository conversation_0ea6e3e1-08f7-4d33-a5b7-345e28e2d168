import { useState, useContext, useEffect } from "react";

// ** MUI Imports
import Grid from "@mui/material/Grid";
import Button from "@mui/material/Button";
import FormControl from "@mui/material/FormControl";
import { Divider } from "@mui/material";
import { AuthContext } from "src/context/AuthContext";

// ** Third Party Imports
import * as yup from "yup";
import { useF<PERSON>, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { FormControlLabel, Switch, Typography } from "@mui/material";

const defaultValues = {
  email: "",
  lastName: "",
  password: "",
  firstName: "",
};

const showErrors = (field, valueLen, min) => {
  if (valueLen === 0) {
    return `${field} field is required`;
  } else if (valueLen > 0 && valueLen < min) {
    return `${field} must be at least ${min} characters`;
  } else {
    return "";
  }
};

const schema = yup.object().shape({
  email: yup.string().email().required(),
  lastName: yup
    .string()
    .min(3, (obj) => showErrors("lastName", obj.value.length, obj.min))
    .required(),
  password: yup
    .string()
    .min(8, (obj) => showErrors("password", obj.value.length, obj.min))
    .required(),
  firstName: yup
    .string()
    .min(3, (obj) => showErrors("firstName", obj.value.length, obj.min))
    .required(),
});

const TenderingTab = ({ onCancel, setter, defaultData, setUnsavedChanges }) => {
  const { updateEntityServices } = useContext(AuthContext);

  // ** States
  const [state, setState] = useState({
    password: "",
    showPassword: false,
  });

  // ** Hook
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues,
    mode: "onChange",
    resolver: yupResolver(schema),
  });
  const [formData, setFormData] = useState(defaultData);
  const [tenderingDocumentsSupported, setTenderingDocumentsSupported] =
    useState(defaultData.tenderingDocuments.isSupported);

  const [
    preQualificationOfDevelopersSupported,
    setPreQualificationOfDevelopersSupported,
  ] = useState(defaultData.preQualificationOfDevelopers.isSupported);

  const [
    scrutinizationOfTenderDocumentsSupported,
    setScrutinizationOfTenderDocumentsSupported,
  ] = useState(defaultData.scrutinizationOfTenderDocuments.isSupported);

  const [
    appointmentOfDevelopersSupported,
    setAppointmentOfDevelopersSupported,
  ] = useState(defaultData.appointmentOfDevelopers.isSupported);

  const [selectAll, setSelectAll] = useState(false);

  const handleSelectAll = (event) => {
    const { checked } = event.target;

    setTenderingDocumentsSupported(checked);
    setPreQualificationOfDevelopersSupported(checked);
    setScrutinizationOfTenderDocumentsSupported(checked);
    setAppointmentOfDevelopersSupported(checked);
    setSelectAll(checked);

    setFormData((prev) => ({
      ...prev,
      tenderingDocuments: { ...prev.tenderingDocuments, isSupported: checked },
      preQualificationOfDevelopers: {
        ...prev.preQualificationOfDevelopers,
        isSupported: checked,
      },
      scrutinizationOfTenderDocuments: {
        ...prev.scrutinizationOfTenderDocuments,
        isSupported: checked,
      },
      appointmentOfDevelopers: {
        ...prev.appointmentOfDevelopers,
        isSupported: checked,
      },
    }));

    setUnsavedChanges(true);
    setIsCheckboxChanged(true);
  };

  useEffect(() => {
    if (
      tenderingDocumentsSupported &&
      preQualificationOfDevelopersSupported &&
      scrutinizationOfTenderDocumentsSupported &&
      appointmentOfDevelopersSupported
    ) {
      setSelectAll(true);
    } else {
      setSelectAll(false);
    }
  }, [
    tenderingDocumentsSupported,
    preQualificationOfDevelopersSupported,
    scrutinizationOfTenderDocumentsSupported,
    appointmentOfDevelopersSupported,
  ]);

  const [isCheckboxChanged, setIsCheckboxChanged] = useState(false);

  const handleCheckboxChange = (event) => {
    setIsCheckboxChanged(true);
    handleOnChange(event);
  };

  const handleOnChange = (event) => {
    const { name, checked } = event.target;

    switch (name) {
      case "tenderingDocuments":
        setFormData({
          ...formData,
          tenderingDocuments: {
            ...formData.tenderingDocuments,
            isSupported: checked,
          },
        });
        break;
      case "preQualificationOfDevelopers":
        setFormData({
          ...formData,
          preQualificationOfDevelopers: {
            ...formData.preQualificationOfDevelopers,
            isSupported: checked,
          },
        });
        break;
      case "scrutinizationOfTenderDocuments":
        setFormData({
          ...formData,
          scrutinizationOfTenderDocuments: {
            ...formData.scrutinizationOfTenderDocuments,
            isSupported: checked,
          },
        });
        break;
      case "appointmentOfDevelopers":
        setFormData({
          ...formData,
          appointmentOfDevelopers: {
            ...formData.appointmentOfDevelopers,
            isSupported: checked,
          },
        });
        break;
    }

    setUnsavedChanges(true);
    setIsCheckboxChanged(true);
  };

  async function onSubmit() {
    console.log("submitted data 2", formData);
    setUnsavedChanges(false);
    const response = await updateEntityServices(
      { tenderingStage: formData },
      () => {
        console.error(" Tendering Stage Details failed");
      }
    );

    if (response) {
      setFormData(response);
      setter(formData);
    }

    console.log("Submitted data from Section3:", response);
    onCancel();
  }

  return (
    <>
      <Grid container sx={{ display: "flex", alignItems: "center" }}>
        <Grid item xs={8} sm={4}>
          <FormControl fullWidth>
            <Typography sx={{ fontWeight: 600 }}>Service Name:</Typography>
          </FormControl>
        </Grid>

        <Grid item xs={9} sm={2}>
          <Typography sx={{ fontWeight: 600 }}>(Yes / No):</Typography>
        </Grid>

        <Grid item xs={12}>
          <Divider sx={{ my: (theme) => `${theme.spacing(2)} !important` }} />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>Tendering Documents:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="tenderingDocuments"
            control={control}
            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={tenderingDocumentsSupported}
                    onChange={(event) => {
                      setTenderingDocumentsSupported(event.target.checked);
                      handleOnChange(event);
                    }}
                    name="tenderingDocuments"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>PreQualification Of Developers:</Typography>
          </FormControl>
        </Grid>

        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="preQualificationOfDevelopers"
            control={control}
            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={preQualificationOfDevelopersSupported}
                    onChange={(event) => {
                      setPreQualificationOfDevelopersSupported(
                        event.target.checked
                      );
                      handleOnChange(event);
                    }}
                    name="preQualificationOfDevelopers"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>Scrutinization Of TenderDocuments:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="scrutinizationOfTenderDocuments"
            control={control}
            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={scrutinizationOfTenderDocumentsSupported}
                    onChange={(event) => {
                      setScrutinizationOfTenderDocumentsSupported(
                        event.target.checked
                      );
                      handleOnChange(event);
                    }}
                    name="scrutinizationOfTenderDocuments"
                  />
                }
              />
            )}
          />
        </Grid>

        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>Appointment Of Developers:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <Controller
            name="appointmentOfDevelopers"
            control={control}
            render={() => (
              <FormControlLabel
                control={
                  <Switch
                    checked={appointmentOfDevelopersSupported}
                    onChange={(event) => {
                      setAppointmentOfDevelopersSupported(event.target.checked);
                      handleOnChange(event);
                    }}
                    name="appointmentOfDevelopers"
                  />
                }
              />
            )}
          />
        </Grid>
        <Grid item xs={9} sm={4}>
          <FormControl fullWidth>
            <Typography>All:</Typography>
          </FormControl>
        </Grid>
        <Grid item xs={3} sm={6} sx={{ mt: 1.5, mb: 1.5 }}>
          <FormControlLabel
            control={
              <Switch
                checked={selectAll}
                onChange={handleSelectAll}
                name="selectAll"
              />
            }
          />
        </Grid>

        <Grid item xs={12} sx={{ mt: 2 }}>
          <center>
            <Button
              size="medium"
              sx={{ mr: 3 }}
              onClick={() => onCancel()}
              variant="outlined"
              color="primary"
            >
              Cancel
            </Button>
            <Button
              size="medium"
              type="button"
              onClick={onSubmit}
              variant="contained"
              disabled={!isCheckboxChanged}
            >
              Save
            </Button>
          </center>
        </Grid>
      </Grid>
    </>
  );
};

export default TenderingTab;
