// ** React Imports
import { useState } from 'react'

// ** MUI Imports
import Tab from '@mui/material/Tab'
import TabList from '@mui/lab/TabList'
import TabPanel from '@mui/lab/TabPanel'
import TabContext from '@mui/lab/TabContext'
import Typography from '@mui/material/Typography'

const NavTabsSiteVisits = props => {
  // ** State
  const [value, setValue] = useState('1')

  // ** Props
  const { tabContent1, tabContent2} = props

  const handleChange = (event, newValue) => {
    setValue(newValue)
  }

  return (

    <TabContext value={value}>
      <TabList
        variant="scrollable"
        scrollButtons="auto"
        onChange={handleChange}
        aria-label="forced scroll tabs example"
      >
        <Tab
          value="1"
          component="a"
          label="Today"
          sx={{ textTransform:"none"}}
          onClick={(e) => e.preventDefault()}
        />
        <Tab
          value="2"
          component="a"
          label="Upcoming"
          sx={{ textTransform:"none"}}
          onClick={(e) => e.preventDefault()}
        />
       
      </TabList>
      <TabPanel value="1" sx={{ pb: 1, pt: 3, px: { xs: "0", md: "0.5rem" } }}>
        <Typography>{tabContent1}</Typography>
      </TabPanel>
      <TabPanel value="2" sx={{ pb: 1, pt: 3, px: { xs: "0", md: "0.5rem" } }}>
        <Typography>{tabContent2}</Typography>
      </TabPanel>
     
    </TabContext>
  );
}

export default NavTabsSiteVisits