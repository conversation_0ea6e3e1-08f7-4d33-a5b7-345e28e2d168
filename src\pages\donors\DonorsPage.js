import { useContext, useEffect, useState } from "react";
import {
  Box,
  CardContent,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Divider,
  IconButton,
  InputAdornment,
  FormControl,
  Grid,
  TextField,
  Typography,
  Menu,
  MenuItem,
} from "@mui/material";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import Icon from "src/@core/components/icon";
import Button from "@mui/material/Button";
import { DataGrid } from "@mui/x-data-grid";
import SearchIcon from "@mui/icons-material/Search";
import axios from "axios";
import { Controller, useForm } from "react-hook-form";
import FallbackSpinner from "src/@core/components/spinner";
import { AuthContext } from "src/context/AuthContext";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import useColumns from "./Columns";
import DeleteDialog from "./DeleteDialog";
import * as XLSX from "xlsx";
import DonorDialog from "./DonorDialog";
import authConfig from "src/configs/auth";
import AdvancedSearch from "./AdvancedSearch";
import BlankLayout from "src/@core/layouts/BlankLayout";
import { useRBAC } from "../permission/RBACContext";
import { useRouter } from "next/router";
import { MENUS, PAGES, PERMISSIONS } from "src/constants";
import ImportExportDonors from "../approve-donors/ImportDialog";
import { Upload } from "@mui/icons-material";
import FileDownloadSharpIcon from "@mui/icons-material/FileDownloadSharp";
import FileUploadSharpIcon from "@mui/icons-material/FileUploadSharp";

// Import new components
import DonorsHeader from "./components/DonorsHeader";
import FilterChips from "./components/FilterChips";
import DonorsDataGrid from "./components/DonorsDataGrid";
import MessageDialog from "./components/MessageDialog";
import StatusDialog from "./components/StatusDialog";

const DonorsPage = () => {
  const {
    canMenuPage,
    canMenuPageSection,
    canMenuPageSectionField,
    rbacRoles,
  } = useRBAC();
  const router = useRouter();

  const canAccessDonors = (requiredPermission) =>
    canMenuPage(MENUS.LEFT, PAGES.DONORS, requiredPermission);

  const [userList, setUserList] = useState([]);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const {
    user,
    donorDetails,
    setListValues,
    setDonorDetails,
    getAllListValuesByListNameId,
  } = useContext(AuthContext);

  const [importOpen, setImportOpen] = useState(false);

  const [searchKeyword, setSearchKeyword] = useState("");
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [currentRow, setCurrentRow] = useState(null);
  const [rowCount, setRowCount] = useState();
  const [tagsList, setTagsList] = useState([]);
  const [keyword, setKeyword] = useState("");
  const [loading, setLoading] = useState(false);
  const [openAdvancedSearch, setOpenAdvancedSearch] = useState(false);
  const [searchingState, setSearchingState] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState([]);
  const [tenantsList, setTenantsList] = useState([]);
  const [valuesUpdate, setValuesUpdate] = useState(false);
  const [menu, setMenu] = useState(null);
  const [sendMessageDialog, setSendMessageDialog] = useState(false);
  const [templates, setTemplates] = useState([]);
  const [emailsList, setEmailsList] = useState([]);
  const [selectedTemplateName, setSelectedTemplateName] = useState("");
  const [sendingMessage, setSendingMessage] = useState(false);
  const [dialogMessage, setDialogMessage] = useState("");
  const [messageStatus, setMessageStatus] = useState(null);
  const [communicationMode, setCommunicationMode] = useState(""); // "WHATSAPP" or "EMAIL"

  const { control } = useForm();

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
  };

  const [selectedRows, setSelectedRows] = useState([]);

  const handleSelectionModelChange = (newSelectionModel) => {
    setSelectedRows(newSelectionModel);
  };

  // Fetch tenants list for SUPER_ADMIN
  useEffect(() => {
    axios({
      method: "get",
      url: getUrl(authConfig.organisationsEndpoint) + "/TENANT",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setTenantsList(
          res.data.map((item) => ({
            value: item.id,
            key: item.name,
          }))
        );
      })
      .catch(() => {
        setTenantsList([]);
      });
  }, []);

  // Fetch WhatsApp templates
  useEffect(() => {
    const token = "YOUR_TOKEN_HERE"; // Replace with actual token
    const fetchTemplates = async () => {
      try {
        const response = await fetch(
          "https://live-mt-server.wati.io/321589/api/v1/getMessageTemplates",
          {
            method: "GET",
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
          }
        );
        const data = await response.json();
        const templateData = data.messageTemplates.map((template) => ({
          key: template?.elementName,
          value: template?.id,
          parameters: template.parameters || [],
          status: template.status,
        }));
        const filteredTemplates = templateData.filter(
          (template) => template.status !== "DELETED"
        );
        setTemplates(filteredTemplates);
      } catch (error) {
        console.error("Error fetching templates:", error);
      }
    };
    fetchTemplates();
  }, []);

  // Fetch email templates
  useEffect(() => {
    axios({
      method: "get",
      url: getUrl(authConfig.donorGroupsEndpoint) + "/email-templates",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setEmailsList(
          res.data.map((item) => ({
            value: item.id,
            key: item.templateName,
          }))
        );
      })
      .catch(() => {
        setEmailsList([]);
      });
  }, []);

  useEffect(() => {
    axios({
      method: "post",
      url: getUrl(authConfig.allListNamesValues) + "?selectType=LIST_VALUES",
    })
      .then((res) => {
        setListValues(res.data.data);
        window.localStorage.setItem(
          authConfig.listValues,
          JSON.stringify(res.data.data)
        );
      })
      .catch((err) => console.log("List values error", err));
  }, [valuesUpdate]);

  // Fetch Donor list
  const fetchDonors = async (currentPage, currentPageSize, selectedFilters) => {
    setLoading(true);
    const url = getUrl(authConfig.donorsEndpoint) + "/all/donor";
    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
    };

    selectedFilters?.forEach((filter) => {
      const key = filter.key;
      data[key] = filter.value;
    });

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data && response.data.donorsResponseList.length > 0) {
        setUserList(response.data.donorsResponseList);
        setRowCount(
          response.data?.rowCount || response.data.donorsResponseList.length
        );
      }
    } catch (err) {
      console.error("Failed to fetch donors:", err);
    }

    setLoading(false);
  };

  useEffect(() => {
    fetchDonors(page, pageSize, selectedFilters);
  }, [page, pageSize, selectedFilters]);

  // Handler functions
  const handleSendWhatsAppClick = () => {
    setCommunicationMode("WHATSAPP");
    setSendMessageDialog(true);
  };

  const handleSendEmailClick = () => {
    setCommunicationMode("EMAIL");
    setSendMessageDialog(true);
  };

  const handleCloseSendMessageDialog = () => {
    setSendMessageDialog(false);
    setSelectedTemplateName("");
    setSendingMessage(false);
    setCommunicationMode("");
  };

  const handleSendMessage = async () => {
    if (!selectedTemplateName || !currentRow) {
      const templateType =
        communicationMode === "EMAIL" ? "email template" : "template";
      setDialogMessage(`Please select a ${templateType}`);
      setMessageStatus("error");
      return;
    }

    setSendingMessage(true);

    const url =
      getUrl(authConfig.donorsEndpoint) +
      "/send-communication/" +
      currentRow?.id;

    const data = {
      communicationModeEnums: communicationMode,
      templateIdOrName: selectedTemplateName,
    };

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: getAuthorizationHeaders(),
        data: data,
      });

      if (response.data === "Communication sent successfully") {
        handleSendMessageSuccess();
      } else {
        handleSendMessageFailure("Failed to send message to donor");
      }
    } catch (error) {
      handleSendMessageFailure(error);
    } finally {
      setSendingMessage(false);
    }
  };

  const handleSendMessageSuccess = () => {
    handleCloseSendMessageDialog();
    const communicationType =
      communicationMode === "EMAIL" ? "Email" : "WhatsApp";
    const message = `
      <div>
        <h3>Message sent successfully to donor via ${communicationType}.</h3>
      </div>
    `;
    setDialogMessage(message);
    setMessageStatus("info");
  };

  const handleSendMessageFailure = (err) => {
    handleCloseSendMessageDialog();
    const communicationType =
      communicationMode === "EMAIL" ? "Email" : "WhatsApp";
    const message = `
      <div>
        <h3>Failed to send message via ${communicationType}. Please try again later.</h3>
      </div>
    `;
    setDialogMessage(message);
    setMessageStatus("error");
  };

  const handleTemplateChange = (event) => {
    const selectedArray =
      communicationMode === "EMAIL" ? emailsList : templates;
    const selectedTemplate = selectedArray.find(
      (item) => item.value === event.target.value
    );
    const templateKey = selectedTemplate
      ? selectedTemplate.key
      : event.target.value;
    setSelectedTemplateName(templateKey);
  };

  // Columns
  const columns = useColumns({
    currentRow,
    setCurrentRow,
    setOpenDialog,
    tenantsList,
    menu,
    setMenu,
    setOpenDeleteDialog,
    setSendWhatsAppDialog: handleSendWhatsAppClick,
    setSendEmailDialog: handleSendEmailClick,
  });

  const handleExportDonors = () => {
    const url = getUrl(authConfig.donorImportEndpoint) + "/export-donors";
    const headers = getAuthorizationHeaders();

    const today = new Date();
    const day = String(today.getDate()).padStart(2, "0");
    const month = String(today.getMonth() + 1).padStart(2, "0");
    const year = today.getFullYear();
    const formattedDate = `${day}-${month}-${year}`;

    axios({
      method: "post",
      url: url,
      headers: headers,
      data: selectedRows,
      responseType: "arraybuffer",
    })
      .then((res) => {
        const workbook = XLSX.read(res.data, { type: "array" });
        const fileName = `donor_export-${formattedDate}.xlsx`;
        XLSX.writeFile(workbook, fileName);
      })
      .catch((error) => {
        console.error("Error exporting donors:", error);
      });
  };

  // Handlers
  const handleSearch = (e) => {
    setKeyword(e.target.value);
    setSearchKeyword(e.target.value);
    setPage(1);
  };

  const handleOpenDialog = () => {
    setCurrentRow(null);
    setOpenDialog(true);
  };

  const handleOpenImportDialog = () => {
    setImportOpen(true);
  };

  const handleAdvancedSearch = () => {
    setOpenAdvancedSearch(!openAdvancedSearch);
  };

  const clearAllFilters = () => {
    setSelectedFilters([]);
    setSearchingState(false);
  };

  const handleApplyFilters = (filters) => {
    setSelectedFilters(filters);
    setSearchingState(true);
  };

  const handleRemoveFilter = (filterKey) => {
    setSelectedFilters((prevFilters) =>
      prevFilters.filter((filter) => filter.key !== filterKey)
    );
  };

  const handlePageChange = (newPage) => {
    setPage(newPage + 1);
  };

  const handlePageSizeChange = (newPageSize) => {
    setPageSize(newPageSize);
    setPage(1);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setDonorDetails(null);
  };

  const handleCloseImportDialog = () => {
    setImportOpen(false);
  };

  const handleError = (error) => {
    console.error("Donors page error:", error);
  };

  const handleMessageStatusClose = () => {
    setMessageStatus(null);
    setDialogMessage("");
  };

  useEffect(() => {
    getAllListValuesByListNameId(
      authConfig.tagsListNameId,
      (data) =>
        setTagsList(
          data?.listValues.map((item) => ({
            value: item.id,
            key: item.listValue,
          }))
        ),
      handleError
    );
  }, [authConfig, valuesUpdate]);

  useEffect(() => {
    if (rbacRoles != null && rbacRoles.length > 0) {
      if (!canAccessDonors(PERMISSIONS.READ)) {
        router.push("/401");
      }
    }
  }, [rbacRoles]);

  if (canAccessDonors(PERMISSIONS.READ)) {
    return (
      <Grid>
        <DonorsHeader
          onAdd={handleOpenDialog}
          onExport={handleExportDonors}
          onImport={handleOpenImportDialog}
          selectedRows={selectedRows}
        />
        <Divider />
        <FilterChips
          selectedFilters={selectedFilters}
          onRemoveFilter={handleRemoveFilter}
        />
        <DonorsDataGrid
          rows={userList}
          columns={columns}
          pageSize={pageSize}
          page={page}
          rowCount={rowCount}
          onSelectionModelChange={handleSelectionModelChange}
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
          rowsPerPageOptions={rowsPerPageOptions}
        />

        <DonorDialog
          open={openDialog}
          onClose={handleCloseDialog}
          formData={donorDetails}
          tenantsList={tenantsList}
          tagsList={tagsList}
          fetchDonors={fetchDonors}
          page={page}
          pageSize={pageSize}
          searchKeyword={selectedFilters}
          setValuesUpdate={setValuesUpdate}
        />

        <ImportExportDonors
          open={importOpen}
          onClose={handleCloseImportDialog}
        />

        <DeleteDialog
          open={openDeleteDialog}
          onClose={handleCloseDeleteDialog}
          data={currentRow}
          fetchDonors={fetchDonors}
          page={page}
          pageSize={pageSize}
          searchKeyword={selectedFilters}
        />

        <MessageDialog
          open={sendMessageDialog}
          onClose={handleCloseSendMessageDialog}
          communicationMode={communicationMode}
          templates={templates}
          emailsList={emailsList}
          selectedTemplateName={selectedTemplateName}
          onTemplateChange={handleTemplateChange}
          onSend={handleSendMessage}
          sendingMessage={sendingMessage}
        />

        <StatusDialog
          open={messageStatus !== null}
          onClose={handleMessageStatusClose}
          message={dialogMessage}
          status={messageStatus}
        />
      </Grid>
    );
  } else {
    return null;
  }
};

export default DonorsPage;
