import React, { useState, useEffect } from 'react'
import Box from '@mui/material/Box'
import Autocomplete from '@mui/material/Autocomplete'
import TextField from '@mui/material/TextField'
import { Typography } from '@mui/material'

const SelectAutoCompleteRole = props => {
  const { id, label, nameArray = [], value, onChange } = props

  // Track local selected value
  const [selectedValue, setSelectedValue] = useState(value || null)

  useEffect(() => {
    // If parent value changes externally, sync local state
    setSelectedValue(value || null)
  }, [value])

  const handleChange = (event, option) => {
    const newValue = option ? option.value : null
    setSelectedValue(newValue)
    // Fire parent's onChange in the same shape as your code expects
    onChange({ target: { value: newValue } })
  }

  return (
    <Autocomplete
      id={id}
      // MUI will handle open/close automatically
      autoHighlight
      options={nameArray}
      getOptionLabel={option => option.key || ''}
      // Match the Autocomplete value to the correct object
      value={
        nameArray?.find(option => option.value === selectedValue) || null
      }
      // MUI's default onChange handles selection
      onChange={handleChange}
      // Removing manual onClick from renderOption
      renderOption={(props, option) => (
        <Box component='li' {...props} sx={{ display: 'flex', flexDirection: 'column',alignItems: 'flex-start', p: 1 ,width: '100%'}}>
          <Typography variant="body1" sx={{ 
              textAlign: 'left', // Explicitly left-align text
              width: '100%', // Ensures alignment works correctly
              display: 'block' // Prevents text from being centered
            }}>
            {option?.key}
          </Typography>
          <Typography variant="body2"  sx={{ 
              color: 'gray', 
              textAlign: 'left', // Explicitly left-align description
              width: '100%', 
              display: 'block'
            }}>
            {option?.description}
          </Typography>
        </Box>
      )}
      renderInput={params => (
        <TextField
          {...params}
          size='small'
          label={label}
          placeholder={label}
          InputLabelProps={{
            ...params.InputLabelProps,
            shrink: true
          }}
        />
      )}
    />
  )
}

export default SelectAutoCompleteRole
