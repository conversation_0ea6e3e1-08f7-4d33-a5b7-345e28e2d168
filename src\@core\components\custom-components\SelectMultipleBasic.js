// ** React Imports
import { useEffect, useState } from 'react';
import React from 'react';

// ** MUI Imports
import Box from '@mui/material/Box';
import Chip from '@mui/material/Chip';
import MenuItem from '@mui/material/MenuItem';
import Typography from '@mui/material/Typography';
import InputLabel from '@mui/material/InputLabel';
import FormControl from '@mui/material/FormControl';
import Select from '@mui/material/Select';

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;

const MenuProps = {
  PaperProps: {
    style: {
      width: 250,
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP
    }
  }
};

const SelectMultipleBasic = props => {
  // ** Props
  const { id, label, nameArray, register, defaultValue } = props;

  // Set the default values
  const [selectedValues, setSelectedValues] = React.useState([]);

  useEffect(() => {
    if (typeof defaultValue === 'string' && defaultValue) {
      setSelectedValues(defaultValue.split(','));
    }
  }, [defaultValue]);

  const handleChange = event => {
    setSelectedValues(event.target.value);
  };

  return (
    <>
      <Box sx={{ display: 'flex', flexDirection: 'column', '& > *': { mt: 0 } }}>
        <div>
          <FormControl fullWidth>
            <InputLabel id={id + '-label'}>{label}</InputLabel>
            <Select
              {...register(id)}
              multiple
              label={label}
              value={selectedValues}
              MenuProps={MenuProps}
              id={id}
              size="small"
              onChange={handleChange}
              labelId={id + '-label'}
              renderValue={selected => (
                <Box sx={{ display: 'flex', flexWrap: 'wrap' }}>
                  {selected.slice(0, 1).map((value, index) => (
                    <Chip key={index} label={value} sx={{ m: 0.75 }} />
                  ))}
                  {selected.length > 1 && (
                    <Chip label={`+${selected.length - 1}`} sx={{ m: 0.75 }} />
                  )}
                </Box>
              )}
            >
              {nameArray.map((name, index) => (
                <MenuItem key={index} value={name.value}>
                  <>{name.name}</>
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </div>
      </Box>
    </>
  );
};

export default SelectMultipleBasic;
