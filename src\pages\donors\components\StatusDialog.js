import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
} from "@mui/material";

const StatusDialog = ({ open, onClose, message, status }) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
      PaperProps={{
        sx: {
          p: (theme) => `${theme.spacing(2.5)} !important`,
          backgroundColor: (theme) => theme.palette.text.primary.main,
        },
      }}
    >
      <Box
        sx={{
          borderRadius: 1,
          textAlign: "center",
          border: (theme) => `1px solid ${theme.palette.divider}`,
          borderColor: "primary.main",
        }}
      >
        <DialogContent>
          <DialogContentText
            id="alert-dialog-description"
            color="primary.main"
            fontWeight={"bold"}
            dangerouslySetInnerHTML={{ __html: message }}
          />
        </DialogContent>
        <DialogActions
          sx={{
            display: "flex",
            justifyContent: "center",
          }}
        >
          <Button onClick={onClose} color="primary">
            OK
          </Button>
        </DialogActions>
      </Box>
    </Dialog>
  );
};

export default StatusDialog; 