import React,{useContext, useEffect, useState} from "react";
import {
  Table,
  TableRow,
  TableCell,
  TableBody,
  Checkbox,
} from "@mui/material";
import { Controller } from "react-hook-form";
import {
  FormControl,
  FormLabel,
  FormControlLabel,
  FormGroup} from "@mui/material";
import { AuthContext } from "src/context/AuthContext";

const accessList = [{ name: "Create", value: "CREATE" }, { name: "Read", value: "READ" },
{ name: "Update", value: "UPDATE" }, { name: "Delete", value: "DELETE" }];

const SiteMapData = ({
  defaultData,
  setValue,
  control,
  getValues
}) => {

  const constAccess = ["CREATE", "READ", "UPDATE", "DELETE"];
  //AuthContext
  const {allSiteMapDataFromSettings } = useContext(AuthContext);

  // Not using this fun but kept for reference.
  const onSubmit = () => {
    const transformedData = data.map((page) => {
      const pageData = {
        name: page.name,
        permissions: accessList.filter((theme) =>
          getValues(`${page.name}.${theme.value}`)
        ).map((theme) => theme.value),
      };

      if (page.children) {
        pageData.children = page.children.map((child) => {
          return {
            name: child.name,
            permissions: accessList.filter((theme) =>
              getValues(`${child.name}.${theme.value}`)
            ).map((theme) => theme.value),
          };
        });
      }

      return pageData;
    });

    console.log(transformedData);
  };

  useEffect(() => {
    // Your mount code here. This code runs when the component is mounted.
    console.log('SiteMapData Component Mounted');

    // Return a function from here. This function will be called when the component is about to unmount.
    return () => {
        console.log('SiteMapData Component will unmount');
        // Your unmount code here. Put your clean-up logic here.
        const executeOperations = async () => {
          await resetCheckboxes(); // Wait for the first operation to complete          
      };
  
      executeOperations();

    };
  }, []); // The empty array means this effect runs once on mount and once on unmount.

  const resetCheckboxes = async () =>{
    // Prepopulate all the checkboxes to un-select first.
    const settingCheckboxesUnSelected = (page) => {
      constAccess.forEach((themeValue) => {
        setValue(`${page.code}.${themeValue}`, false);
      });

      if (page?.children) {
        page.children.forEach((child) => {
          settingCheckboxesUnSelected(child);
        });
      }
    };

    allSiteMapDataFromSettings?.forEach((page) => {
      settingCheckboxesUnSelected(page);
    });
  }

  const prePopulateDefaultData = async (defaultData) => {
    // Prepopulate checkboxes based on default values specified for each page and children
    const collectDefaultValues = (page) => {
      page?.permissions?.forEach((themeValue) => {
        setValue(`${page.code}.${themeValue}`, true);
      });

      if (page?.children) {
        page.children.forEach((child) => {
          collectDefaultValues(child);
        });
      }
    };

    defaultData?.forEach((page) => {
      collectDefaultValues(page);
    });
  }



  useEffect(() => {

    console.log("Sitemap Data DefaultData:", defaultData);
    const executeOperations = async () => {
        await resetCheckboxes(); // Wait for the first operation to complete
        await prePopulateDefaultData(defaultData); // Then start the second operation
    };

    executeOperations();
    
  }, [setValue, defaultData]);
 
  return (
    <div>
      <Table key={"sitemap-data-table"}>
        <TableBody key="sitemap-data-table-body">
        {allSiteMapDataFromSettings.map((page, index) => (
              <TableRow style={{ height: '10px' }} key={page.name + "-" + index + "-tablerow"}>
            
              <TableCell key={page.name + "-" + index + "-tablecell1"}>
                <FormLabel component="legend" focused={false}>
                  {page.name}
                </FormLabel>
              </TableCell>
              <TableCell key={page.name + "-" + index + "-tablecell2"}>
              <FormControl
              fullWidth
              component="fieldset"
              margin="normal"
            >
              <FormGroup aria-label="Themes" row={true}>
                {accessList.map((theme) => (                    
                  <FormControlLabel
                    key={theme.value}
                    label={theme.name}
                    control={
                      <Controller
                        name={`${page.code}.${theme.value}`}
                        control={control}
                        render={({ field }) => (
                          <Checkbox
                            {...field}
                            checked={field.value || false}
                            onChange={(e) => field.onChange(e.target.checked)}
                          />
                        )}
                      />
                    }
                  />
                ))}
              </FormGroup>              
              </FormControl>
                  </TableCell>            
            </TableRow>
        ))}
        </TableBody>        
      </Table>
    </div>
  );
};

export default SiteMapData;
