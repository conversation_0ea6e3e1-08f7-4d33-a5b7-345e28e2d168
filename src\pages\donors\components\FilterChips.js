import { Box, Chip } from "@mui/material";

const FilterChips = ({ selectedFilters, onRemoveFilter }) => {
  return (
    <Box sx={{ display: "flex", flexWrap: "wrap", mb: 2 }}>
      {selectedFilters.map((filter) => (
        <Chip
          key={filter.key}
          label={`${filter.label}: ${filter.value}`}
          onDelete={() => onRemoveFilter(filter.key)}
          sx={{ mr: 1, mb: 1 }}
        />
      ))}
    </Box>
  );
};

export default FilterChips; 