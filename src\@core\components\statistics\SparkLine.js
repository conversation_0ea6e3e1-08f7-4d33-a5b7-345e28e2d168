import React, { useState, useEffect } from "react";
import dynamic from "next/dynamic";
import Grid from "@mui/material/Grid";
import { useMediaQuery } from "@mui/material";

const ApexChart = dynamic(() => import("react-apexcharts"), { ssr: false });

const SparkLines = () => {
  const [data, setData] = useState({
    invoiceStages: [8, 5, 12, 3, 15],
    paymentStages: [10, 18, 15, 30, 20],
    satisfactionRatings: [3.5, 4.0, 4.2, 4.5, 4.3],
    pendingPayments: [5, 10, 8, 12, 14],
  });

  const isXs = useMediaQuery("(max-width:600px)");

  const getAxesConfiguration = () => {
    if (isXs) {
      return {
        xaxis: {
          categories: [
            "No of Invoices",
            "No of Payments",
            "Satisfaction Rating",
            "Pending Payments",
            "Other Metrics",
          ],
          labels: {
            rotate: -45,
          },
        },
        yaxis: [
          {
            seriesName: "Invoice Stages",
            axisTicks: { show: true },
            axisBorder: { show: true, color: "#008FFB" },
            labels: { style: { colors: "#008FFB" } },
            title: { text: "Number of Invoices", style: { color: "#008FFB" } },
          },
          {
            seriesName: "Payment Stages",
            opposite: true,
            axisTicks: { show: true },
            axisBorder: { show: true, color: "#00E396" },
            labels: { style: { colors: "#00E396" } },
            title: { text: "Number of Payments", style: { color: "#00E396" } },
          },
        ],
      };
    } else {
      return {
        xaxis: {
          categories: [
            "No of Invoices",
            "No of Payments",
            "Satisfaction Rating",
            "Pending Payments",
            "Other Metrics",
          ],
          labels: {
            rotate: -45,
          },
        },
        yaxis: [
          {
            seriesName: "Invoice Stages",
            axisTicks: { show: true },
            axisBorder: { show: true, color: "#008FFB" },
            labels: { style: { colors: "#008FFB" } },
            title: { text: "Number of Invoices", style: { color: "#008FFB" } },
          },
          {
            seriesName: "Payment Stages",
            opposite: true,
            axisTicks: { show: true },
            axisBorder: { show: true, color: "#00E396" },
            labels: { style: { colors: "#00E396" } },
            title: { text: "Number of Payments", style: { color: "#00E396" } },
          },
          {
            seriesName: "Satisfaction Rating (Goals)",
            opposite: true,
            axisTicks: { show: true },
            axisBorder: { show: true, color: "#FEB019" },
            labels: { style: { colors: "#FEB019" } },
            title: { text: "Rating (out of 5)", style: { color: "#FEB019" } },
          },
          {
            seriesName: "Pending Payments",
            opposite: true,
            axisTicks: { show: true },
            axisBorder: { show: true, color: "#FF4560" },
            labels: { style: { colors: "#FF4560" } },
            title: { text: "Pending Payments", style: { color: "#FF4560" } },
          },
        ],
      };
    }
  };

  const axesConfig = getAxesConfiguration();

  const state = {
    series: [
      {
        name: "Invoice",
        type: "column",
        data: data.invoiceStages,
      },
      {
        name: "Payment",
        type: "column",
        data: data.paymentStages,
      },
      {
        name: "Satisfaction Rating",
        type: isXs ? "column" : "line",
        data: data.satisfactionRatings,
      },
      {
        name: "Pending Payments",
        type: isXs ? "column" : "line",
        data: data.pendingPayments,
      },
    ],
    options: {
      chart: {
        height: 350,
        type: "line",
        stacked: false,
        zoom: {
          enabled: true,
        },
      },
      dataLabels: { enabled: false },
      stroke: { width: isXs ? [1, 1, 0, 0] : [1, 1, 4, 2] },
      colors: ["#008FFB", "#00E396", "#FEB019", "#FF4560"],
      xaxis: axesConfig.xaxis,
      yaxis: axesConfig.yaxis,
      tooltip: {
        shared: true,
        intersect: false,
        followCursor: true,
      },
      legend: {
        horizontalAlign: "left",
        offsetX: 40,
      },
      annotations: {
        yaxis: [
          {
            y: 20,
            borderColor: "#FF4560",
            label: {
              text: "Target Reached",
              style: {
                color: "#fff",
                background: "#FF4560",
              },
            },
          },
        ],
      },
    },
  };

  return (
    <Grid container spacing={2}>
      <Grid item xs={12} sm={12} md={8.9} lg={12} xl={12}>
        <div>
          <ApexChart
            options={state.options}
            series={state.series}
            type="line"
            height={350}
          />
        </div>
      </Grid>
    </Grid>
  );
};

export default SparkLines;
