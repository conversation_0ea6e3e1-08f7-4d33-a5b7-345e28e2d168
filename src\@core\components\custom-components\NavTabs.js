import { useState } from 'react'

// ** MUI Imports
import Tab from '@mui/material/Tab'
import TabList from '@mui/lab/TabList'
import TabPanel from '@mui/lab/TabPanel'
import TabContext from '@mui/lab/TabContext'
import Typography from '@mui/material/Typography'


const NavTabs = props => {
  // ** State
  const [value, setValue] = useState('1')

  const { tabContent, tabContent2,currentTab, handleTabChange } = props

  const handleChange = (event, newValue) => {
    setValue(newValue)
  }

  return (
    <TabContext value={currentTab}>
      <TabList onChange={handleTabChange} aria-label='nav tabs example'>
        <Tab value='1' component='a' label='Accounts and Audit' href='/drafts' onClick={e => e.preventDefault()} />
        <Tab value='2' component='a' label='Home Buyer Services' href='/trash' onClick={e => e.preventDefault()} />

      </TabList>
      <TabPanel value='1'>
        <Typography>
        {tabContent}
        </Typography>
      </TabPanel>
      <TabPanel value='2'>
        <Typography>
        {tabContent2}
        </Typography>
      </TabPanel>

    </TabContext>
  )
}

export default NavTabs
