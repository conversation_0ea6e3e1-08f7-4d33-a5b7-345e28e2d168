import Box from "@mui/material/Box";
import MenuItem from "@mui/material/MenuItem";
import InputLabel from "@mui/material/InputLabel";
import FormControl from "@mui/material/FormControl";
import Select from "@mui/material/Select";
import { Controller } from "react-hook-form";
import { useEffect } from "react";

const SelectCategory = (props) => {
  const {
    id,
    label,
    nameArray,
    register,
    value,
    onChange,
    clearErrors,
    defaultValue,
  } = props;

  const isOptional = id === "design" || id === "liasoning" || id === "ward" || id === "society" || id === "location" || id === "type" || id === "noOfSalesTeamMembers"; 

  const handleOnChange = (event) => {
    onChange(event);
    clearErrors && clearErrors(id);  
  };

  useEffect(()=>{
    console.log("Selected::::::::", defaultValue, value);
  }, [defaultValue, value])

  return (
    <Box sx={{ "& > *": { mt: 2, mr: 6 } }}>
      <FormControl sx={{ maxWidth: "100%", minWidth: "100%" }}>
      <InputLabel id={`${id}-label`} sx={{fontSize:'0.9rem'}}>{label}</InputLabel>
        <Select
          {...register(id, { required: isOptional ? false : true })} 
          size='small'
          label={label}
          defaultValue={defaultValue || ""}
          id={id}
          labelId={id + "-label"}
          value={value || ""}
          onChange={(e) => {
            handleOnChange(e);
          }}
        >
          {nameArray?.map((name) => (
            <MenuItem key={name.value} value={name.value}>
              {name.key}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
    </Box>
  );
};

export default SelectCategory;
