import React, { useRef } from 'react';
import { IconButton } from '@mui/material';
import toast, { Toaster } from "react-hot-toast";

const navigateToGoogleMaps = () => {
  const url = 'https://maps.google.com/?authuser=0';
  window.open(url, '_blank');
};

const GoogleMapsIconButton = () => {
  const toastIdRef = useRef(null); // Ref to track the active toast ID

  const handleIconHover = () => {
    // Dismiss any active toast
    if (toastIdRef.current) {
      toast.dismiss(toastIdRef.current);
      toastIdRef.current = null;
    }

    // Display the toast
    toastIdRef.current = toast(
      "Click on the icon to navigate to Google Maps.\n1. Search the location.\n2. Click on 'Share'.\n3. Copy the URL.\n4. Paste it in the field.",
      {
        id: "google-maps-toast", // Unique ID
        duration: 5000, // Auto-close after 5 seconds
        style: {
          width: '280px',
          fontSize: '14px',
          padding: '8px',
          whiteSpace: 'pre-wrap',
          lineHeight: '1.4',
        },
      }
    );
  };

  return (
    <>
      <IconButton
        onClick={navigateToGoogleMaps}
        onMouseEnter={handleIconHover}
        edge="end"
        sx={{ cursor: 'pointer' }}
      >
        <img
          src="/images/google-maps-logo.webp"
          alt="Google Maps icon"
          style={{ width: 24, height: 24 }}
          loading="lazy" // Lazy load the image
        />
      </IconButton>
    </>
  );
};

export default GoogleMapsIconButton;
