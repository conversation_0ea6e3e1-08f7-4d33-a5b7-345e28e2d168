import dynamic from "next/dynamic";
import React from "react";

// Dynamically import 'react-apexcharts' to avoid SSR issues
const ApexChart = dynamic(() => import("react-apexcharts"), { ssr: false });

const TreeMapChart = () => {
  const state = {
    series: [
      {
        data: [
          { x: "Andheri", y: 320 },
          { x: "Bandra", y: 280 },
          { x: "Juhu", y: 150 },
          { x: "Colaba", y: 120 },
          { x: "Dadar", y: 220 },
          { x: "Goregaon", y: 110 },
          { x: "Kurla", y: 200 },
          { x: "Malad", y: 90 },
          { x: "<PERSON><PERSON>", y: 130 },
          { x: "Thane", y: 170 },
          { x: "Powai", y: 180 },
          { x: "Vashi", y: 100 },
          { x: "Borivali", y: 140 },
        ],
      },
    ],
    options: {
      legend: {
        show: false,
      },
      chart: {
        height: 350,
        type: "treemap",
      },
      title: {
        text: "Market Share of SPs",
        align: "center",
      },
      colors: [
        '#3B93A5',
        '#F7B844',
        '#ADD8C7',
        '#EC3C65',
        '#CDD7B6',
        '#C1F666',
        '#D43F97',
        '#1E5D8C',
        '#421243',
        '#7F94B0',
        '#EF6537',
        '#C0ADDB',
        '#A0522D' 
      ],
      plotOptions: {
        treemap: {
          distributed: true,
          enableShades: false,
        },
      },
    },
  };

  return (
    <div style={{ width: "100%", display: "flex", justifyContent: "center" }}>
      <div id="chart">
        <ApexChart
          options={state.options}
          series={state.series}
          type="treemap"
          height={350}
        />
      </div>
    </div>
  );
};

export default TreeMapChart;
