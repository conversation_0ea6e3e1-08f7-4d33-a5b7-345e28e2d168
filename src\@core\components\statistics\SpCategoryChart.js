import dynamic from 'next/dynamic';
import { useTheme, useMediaQuery, Card } from '@mui/material';
import { useContext, useEffect, useState } from 'react';
import axios from 'axios';
import authConfig from "src/configs/auth";
import { getAuthorizationHeaders, getUrl } from 'src/helpers/utils';
import { AuthContext } from 'src/context/AuthContext';
import { useRouter } from 'next/router';

// Dynamically import 'react-apexcharts' to avoid SSR issues
const ApexChart = dynamic(() => import('react-apexcharts'), { ssr: false });

const SpCategoryChart = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm")); // Detect if screen size is mobile
  const { listValues,user } = useContext(AuthContext)
  const [sampleData,setSampleData] = useState([])

   const router = useRouter();

  useEffect(() => {
     let url;
            if (user?.roleId === authConfig?.superAdminRoleId) {
              url =
                getUrl(authConfig.statisticsEndpointGraphs) +
                "/admin/service-provider-count-group-by-lead-status";
            } else {
              url =
                getUrl(authConfig.statisticsEndpointGraphs) +
                "/service-provider-count-group-by-lead-status";
            }
        
            let headers;
        
            if (user?.roleId === authConfig?.superAdminRoleId) {
              headers = getAuthorizationHeaders({
                accept: authConfig?.STATISTICS_GET_GROUP_BY_LEAD_STATUS_ADMIN_V1,
              });
            } else {
              headers = getAuthorizationHeaders({
                accept: authConfig?.STATISTICS_GET_GROUP_BY_LEAD_STATUS_EMPLOYEE_V1,
              });
            }
        
    axios({
      method: "get",
      url: url,
      headers: headers,
    })
      .then((res) => {
        setSampleData(res?.data);
      })
      .catch((err) => console.log("Statistics error", err));
  }, []);

  const categories = sampleData?.map(
    (item) => listValues?.find((listItem) => listItem.id === item.leadStatusId)?.name || item.leadStatusId
  );

  const counts = sampleData?.map((item) => item.count); 

  const handleBarClick = (event, chartContext, config) => {
    const clickedIndex = config.dataPointIndex;
    const clickedStatusId = sampleData[clickedIndex]?.leadStatusId;

    if (clickedStatusId) {
      // Navigate to a specific page with the clicked zoneId
      router.push({
        pathname: "/SP",
        query: { leadStatus: clickedStatusId },
      });
    }
  };

  const options = {
    chart: {
      id: "sp-lead-status-bar-chart",
      type: "bar",
      stacked: false,
      events: {
        dataPointSelection: handleBarClick, // Add click event handler
      },
    },
    title: {
      text: "SP's by Lead Status",
      align: "center",
      style: {
        fontSize: isMobile ? "10px" : "18px", // Adjust font size based on screen size
      },
    },
    xaxis: {
      categories: categories,
      labels: {
        style: {
          fontSize: isMobile ? "10px" : "12px", // Adjust x-axis label font size
        },
      },
    },
    yaxis: {
      title: {
        text: "No. of SP's",
        style: {
          fontSize: isMobile ? "10px" : "14px", // Adjust y-axis title size
        },
      },
      labels: {
        style: {
          fontSize: isMobile ? "10px" : "12px", // Adjust y-axis label font size
        },
      },
    },
    plotOptions: {
      bar: {
        columnWidth: "40%",
        distributed: true, // Enable distributed bars for different colors
        dataLabels: {
          position: "top",
        },
      },
    },
    colors: [
      "#008FFB",
      "#FEB019",
      "#FF4560",
      "#00E396",
      "#775DD0",
      "#FF66C3",
      "#1E90FF",
      "#FFC300",
      "#C70039",
      "#900C3F",
    ], // Specify different colors for each bar
    dataLabels: {
      enabled: true,
      style: {
        colors: ["#000"],
        fontSize: isMobile ? "10px" : "12px", // Adjust data label font size
      },
    },
    legend: {
      show: false, // Hide legend for distributed bars
    },
    tooltip: {
      shared: false,
      intersect: true,
    },
  };

  const series = [
    {
      name: "Number of SP's",
      data: counts || [],
    },
  ];

  return (
    <Card sx={{ p: 3 }}>
      <ApexChart options={options} series={series} type="bar" height={400} />
    </Card>
  );
};

export default SpCategoryChart;
