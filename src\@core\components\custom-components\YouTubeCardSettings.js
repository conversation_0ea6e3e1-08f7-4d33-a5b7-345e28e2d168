import React, { useState } from 'react';
import Card from '@mui/material/Card';
import { Box, Grid, Typography, useMediaQuery } from '@mui/material';
import { styled } from '@mui/material/styles'

const VideoFrame = styled('iframe')(({ theme }) => ({
  [theme.breakpoints.down('md')]: {
    borderRadius: '8px',
    width: '100%',
    height: 'auto',
  },
  [theme.breakpoints.up('md')]: {
    borderRadius: '10px',
    width: '100%',
    height: 136,
  },
}))

const YouTubeCardSettings = ({ videoUrl, title }) => {


  const [isPlaying, setIsPlaying] = useState(false);

  const hidden = useMediaQuery(theme => theme.breakpoints.down('md'))

  // Extract the video ID from the URL
  let videoId = '';
  if (videoUrl.includes('youtu.be')) {
    videoId = videoUrl.split('/').pop();
  } else {
    videoId = new URL(videoUrl).searchParams.get('v');
  }
  const embedUrl = `https://www.youtube.com/embed/${videoId}?autoplay=${isPlaying ? '1' : '0'}`;

  const handleClick = () => {
    setIsPlaying(true);
  };

  return (
    <Box
      onClick={handleClick}
      component={"div"}
      sx={{
        display: "flex",
        flexDirection: "column", // Ensures title is below the VideoFrame
        alignItems: "center", // Centers content horizontally
        borderRadius: 1,
        width: "100%",
        cursor: "pointer",
        border: "1px solid #ddd",
      }}
    >
      <VideoFrame title="YouTube Video" src={embedUrl} allowFullScreen />

      <Typography
      variant="body2"
            sx={{
              margin: '8px',
              textAlign: "center",
              cursor: "pointer",
              fontSize: "1.015rem",
              fontWeight: 500,
              "&:hover": {
                textDecoration: "none",
                color: "primary.main",
              },
              height: 80 
         }}
      >
        {title}
      </Typography>
    </Box>
  );
};

export default YouTubeCardSettings;