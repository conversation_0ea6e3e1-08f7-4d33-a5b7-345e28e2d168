import React, { useState } from "react";
import dynamic from "next/dynamic";
import dayjs from "dayjs";
import isBetween from "dayjs/plugin/isBetween";

dayjs.extend(isBetween);

const ApexChart = dynamic(() => import("react-apexcharts"), { ssr: false });

const SocietyOnboardingChart = () => {
  // State for selected date range (Initially Empty)
  const [fromDate, setFromDate] = useState("");
  const [toDate, setToDate] = useState("");

  // List of all possible categories
  const allCategories = ["Onboarded", "Meetings Happened", "Requisition Raised", "Total Conversations"];

  // Mocked onboarding data with different event categories
  const onboardingData = [
    { date: "2025-01-01", category: "Onboarded", count: 10 },
    { date: "2025-01-05", category: "Meetings Happened", count: 12 },
    { date: "2025-01-10", category: "Requisition Raised", count: 15 },
    { date: "2025-01-15", category: "Total Conversations", count: 18 },
    { date: "2025-01-20", category: "Onboarded", count: 22 },
    { date: "2025-01-25", category: "Meetings Happened", count: 25 },
    { date: "2025-02-01", category: "Requisition Raised", count: 30 },
    { date: "2025-02-05", category: "Total Conversations", count: 20 },
    { date: "2025-02-10", category: "Onboarded", count: 35 },
    { date: "2025-02-15", category: "Meetings Happened", count: 30 },
    { date: "2025-02-20", category: "Requisition Raised", count: 40 },
    { date: "2025-02-25", category: "Total Conversations", count: 45 },
    { date: "2025-02-25", category: "Requisition Raised", count: 45 },
    { date: "2025-03-01", category: "Onboarded", count: 50 },
    { date: "2025-03-05", category: "Meetings Happened", count: 55 },
    { date: "2025-03-10", category: "Requisition Raised", count: 60 },
    { date: "2025-03-15", category: "Total Conversations", count: 65 },
    { date: "2025-03-20", category: "Onboarded", count: 80 },
    { date: "2025-03-25", category: "Meetings Happened", count: 85 },
    { date: "2025-03-30", category: "Requisition Raised", count: 90 },
  ];

  // Filter Data: If no dates are selected, show all data
  const filteredData = onboardingData.filter((entry) => {
    if (!fromDate || !toDate) return true; // Show all if dates are empty
    return dayjs(entry.date).isBetween(dayjs(fromDate), dayjs(toDate), null, "[]");
  });

  // Initialize category counts with 0
  const categoryCounts = allCategories.reduce((acc, category) => {
    acc[category] = 0; // Ensure all categories start at 0
    return acc;
  }, {});

  // Aggregate count per category within the date range
  filteredData.forEach((entry) => {
    categoryCounts[entry.category] += entry.count;
  });

  // Extract categories (X-axis) and series data (Y-axis)
  const categories = Object.keys(categoryCounts);
  const seriesData = Object.values(categoryCounts);

  const options = {
    chart: {
      type: "bar",
      height: 350,
      toolbar: { show: false },
    },
    xaxis: {
      categories, // Showing "Onboard, Meetings Happened, etc."
      title: { text: "CHS Count" },
    },
    yaxis: {
      title: { text: "Category" },
    },
    plotOptions: { bar: { horizontal: true } },
    colors: ["rgb(20, 184, 20)"],
  };

  return (
    <div style={{ width: "100%" }}>
      {/* Header */}
      <h3 style={{ color: "black" }}>Society Member</h3>

      {/* Date Pickers (Using Native Input Type) */}
      <div style={{ display: "flex", gap: "10px", marginBottom: "15px" }}>
        <label>
          From:
          <input
            type="date"
            value={fromDate}
            onChange={(e) => setFromDate(e.target.value)}
            placeholder="Select From Date"
          />
        </label>
        <label>
          To:
          <input
            type="date"
            value={toDate}
            onChange={(e) => setToDate(e.target.value)}
            placeholder="Select To Date"
          />
        </label>
      </div>

      {/* Chart */}
      <ApexChart
        options={options}
        series={[{ name: "Count", data: seriesData }]}
        type="bar"
        height={350}
      />
    </div>
  );
};

export default SocietyOnboardingChart;
