import { DataGrid } from "@mui/x-data-grid";
import { CardContent } from "@mui/material";

const DonorsDataGrid = ({
  rows,
  columns,
  pageSize,
  page,
  rowCount,
  onSelectionModelChange,
  onPageChange,
  onPageSizeChange,
  rowsPerPageOptions,
}) => {
  return (
    <CardContent>
      <div style={{ height: 430, width: "100%" }}>
        <DataGrid
          rows={rows}
          columns={columns}
          checkboxSelection
          onSelectionModelChange={onSelectionModelChange}
          pageSize={pageSize}
          page={page - 1}
          rowsPerPageOptions={rowsPerPageOptions}
          rowCount={rowCount}
          paginationMode="server"
          onPageChange={onPageChange}
          onPageSizeChange={onPageSizeChange}
          rowHeight={38}
          headerHeight={38}
        />
      </div>
    </CardContent>
  );
};

export default DonorsDataGrid; 