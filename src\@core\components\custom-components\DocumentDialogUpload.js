import React, { useEffect } from "react";
import { useState } from "react";
import {
  <PERSON>,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  CircularProgress,
  IconButton,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";

import FileInput from "src/@core/components/custom-components/FileUploadFrom";

const DocumentUploadDialog = ({
  open,
  onClose,
  onSave,
  selectedFiles,
  setSelectedFiles,
  loading,
}) => {
  const handleClose = () => {
    onClose();
  };

  const [disableButton, setDisableButton] = useState(true);
  useEffect(() => {
    setDisableButton(selectedFiles.length === 0);
  }, [selectedFiles]);

  const handleSave = () => {
    if (!disableButton) {
      setDisableButton(true);
      if (onSave) {
        onSave();
      }
    }
  };
  return (
    <Dialog
      fullWidth
      maxWidth="md"
      scroll="paper"
      open={open}
      onClose={(_event, reason) => {
        if (reason !== "backdropClick") {
          handleClose();
        }
      }}
    >
      <DialogTitle
        sx={{
          position: "relative",
          borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
          p: (theme) => `${theme.spacing(1.5)} !important`,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
        textAlign={"center"}
        fontSize={"30px !important"}
        fontWeight={"bold"}
      >
        Upload Your Images
        <Box
          sx={{
            position: "absolute",
            top: "4px",
            right: "14px",
          }}
        >
          <IconButton
            size="small"
            onClick={handleClose}
            sx={{
              p: "0.438rem",
              borderRadius: 1,
              color:"common.white", 
              backgroundColor: "primary.main",
              "&:hover": {
                backgroundColor:
                '#66BB6A',
                transition: 'background 0.5s ease, transform 0.5s ease',             
                          },
            }}
          >
            <CloseIcon fontSize="small" />
          </IconButton>
        </Box>
      </DialogTitle>
      <DialogContent
        sx={{
         
          position: "relative",
          ":playing": (theme) => `${theme.spacing(4)} !important`,
          px: (theme) => [
            `${theme.spacing(6)} !important`,
            `${theme.spacing(10)} !important`,
          ],
        }}
      >
        <FileInput 
          sx={{
            justifyContent: "flex-end",
          }}
          selectedFiles={selectedFiles}
          setSelectedFiles={setSelectedFiles}
        />
      </DialogContent>
      <DialogActions
        sx={{
          mr : 2,
          justifyContent: "flex-end",
          borderTop: (theme) => `1px solid ${theme.palette.divider}`,
          p: (theme) => `${theme.spacing(1.85)} !important`,
        }}
      >
        <Button onClick={handleClose}>Close</Button>
        <Button disabled={disableButton} onClick={handleSave}>
          {loading ? <CircularProgress color="inherit" size={24} /> : "Save"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default DocumentUploadDialog;
